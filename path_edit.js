// 路径编辑工作台 - 独立JavaScript文件
// Global variables for ThreeJS
let scene, camera, renderer, controls;
let currentSample = null;
let selectedPath = null;
let selectedPoint = null;
let isDragging = false;
let isRotating = false;
let editMode = 'move'; // 'move' or 'rotate'
let raycaster = new THREE.Raycaster();
let mouse = new THREE.Vector2();
let pathThickness = 3; // 全局路径粗细变量
let image_host = 'https://word.sszhai.com';
let pointSize = 0.12; // 路径点大小
let pathOpacity = 0.8; // 路径透明度

// 2D Canvas相关变量
let canvas2D, ctx2D;
let viewMode = '3d'; // '3d' or '2d'
let canvas2DScale = 1;
let canvas2DOffset = { x: 0, y: 0 };
let selectedPoint2D = null;
let mouse2D = { x: 0, y: 0 };

// 操作历史记录
let operationHistory = [];

// GUI工具栏状态
let guiToolbarCollapsed = false;

// API配置
const API_CONFIG = {
    baseUrl: 'https://word.sszhai.com/api/index',
    endpoints: {
        samples: '/sample?server=1',
        sampleData: '/simples?server=1&id='
    }
};

// API管理模块
const apiManager = {
    // 获取样本列表
    async fetchSamples() {
        try {
            console.log('正在从API获取样本列表...');
            const response = await fetch(`${API_CONFIG.baseUrl}${API_CONFIG.endpoints.samples}`, {
                method: 'GET',
                mode: 'cors',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('API返回样本列表:', result);

            if (result.code === 1 && result.data && result.data.code === 1) {
                return result.data.data;
            } else {
                throw new Error(result.msg || '获取样本列表失败');
            }
        } catch (error) {
            console.error('获取样本列表失败:', error);
            showNotification('获取样本列表失败: ' + error.message, 'error');
            return [];
        }
    },

    // 获取样本数据
    async fetchSampleData(sampleId) {
        try {
            console.log(`正在从API获取样本${sampleId}的数据...`);
            const response = await fetch(`${API_CONFIG.baseUrl}${API_CONFIG.endpoints.sampleData}${sampleId}`, {
                method: 'GET',
                mode: 'cors',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log(`API返回样本${sampleId}数据:`, result);

            if (result.code === 1 && result.data && result.data.code === 1) {
                return result.data.data;
            } else {
                throw new Error(result.msg || '获取样本数据失败');
            }
        } catch (error) {
            console.error('获取样本数据失败:', error);
            showNotification('获取样本数据失败: ' + error.message, 'error');
            return [];
        }
    }
};

// 状态管理模块
const stateManager = {
    // 保存当前样本状态到localStorage
    saveCurrentSample(sampleId, sampleData) {
        try {
            const state = {
                sampleId: sampleId,
                sampleData: sampleData,
                timestamp: Date.now()
            };
            localStorage.setItem('worldModel_currentSample', JSON.stringify(state));
            console.log('样本状态已保存:', sampleId);
        } catch (error) {
            console.error('保存样本状态失败:', error);
        }
    },

    // 从localStorage加载当前样本状态
    loadCurrentSample() {
        try {
            const stateStr = localStorage.getItem('worldModel_currentSample');
            if (stateStr) {
                const state = JSON.parse(stateStr);
                // 检查状态是否过期（24小时）
                if (Date.now() - state.timestamp < 24 * 60 * 60 * 1000) {
                    console.log('加载保存的样本状态:', state.sampleId);
                    return state;
                } else {
                    console.log('样本状态已过期，清除缓存');
                    this.clearCurrentSample();
                }
            }
        } catch (error) {
            console.error('加载样本状态失败:', error);
        }
        return null;
    },

    // 清除当前样本状态
    clearCurrentSample() {
        try {
            localStorage.removeItem('worldModel_currentSample');
            console.log('样本状态已清除');
        } catch (error) {
            console.error('清除样本状态失败:', error);
        }
    }
};

// 数据管理模块
const dataManager = {
    // 缓存的样本列表
    cachedSamples: [],
    // 缓存的样本数据
    cachedSampleData: new Map(),
    // 路径颜色配置
    pathColors: ['#06b6d4', '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899', '#14b8a6'],

    // 获取样本列表
    async getSamples() {
        if (this.cachedSamples.length === 0) {
            this.cachedSamples = await apiManager.fetchSamples();
        }
        return this.cachedSamples;
    },

    // 获取样本数据并转换格式
    async getSampleData(sampleId) {
        if (!this.cachedSampleData.has(sampleId)) {
            const rawData = await apiManager.fetchSampleData(sampleId);
            const convertedData = this.convertApiDataToInternalFormat(rawData, sampleId);
            this.cachedSampleData.set(sampleId, convertedData);
        }
        return this.cachedSampleData.get(sampleId);
    },

    // 将API返回的数据转换为内部格式
    convertApiDataToInternalFormat(apiData, sampleId) {
        console.log('开始转换API数据:', apiData);
        console.log('API数据长度:', apiData ? apiData.length : 0);

        if (!apiData || !Array.isArray(apiData)) {
            console.error('API数据无效:', apiData);
            return null;
        }

        // 找到对应的样本信息
        const sampleInfo = this.cachedSamples.find(s => s.id == sampleId);

        const convertedData = {
            id: sampleId,
            name: sampleInfo ? sampleInfo.name : `样本 ${sampleId}`,
            description: sampleInfo ? sampleInfo.remark || '路径规划样本' : '路径规划样本',
            type: 'api_sample',
            difficulty: 'medium',
            totalPaths: apiData.length,
            estimatedTime: `${(apiData.length * 0.8).toFixed(1)}分钟`,
            image: sampleInfo ? sampleInfo.image : null,
            paths: []
        };

        console.log('准备转换', apiData.length, '条路径数据');

        // 转换路径数据
        apiData.forEach((pathData, index) => {
            console.log(`转换第${index + 1}条路径:`, pathData);
            const path = {
                id: `path-${pathData.id}`,
                name: pathData.name || `路径 ${index + 1}`,
                color: this.pathColors[index % this.pathColors.length],
                type: this.getPathTypeFromStatus(pathData.status),
                visible: true,
                status: pathData.status,
                video: pathData.vadio || '',
                sample_id: pathData.sample_id,
                points: []
            };

            // 转换路径点数据
            if (pathData.path_points && Array.isArray(pathData.path_points)) {
                path.points = pathData.path_points.map(point => ({
                    id: point.id,
                    x: point.x,
                    y: point.y,
                    z: point.z,
                    angle: point.angle,
                    flag: point.flag,
                    speed: 30
                }));
            }

            convertedData.paths.push(path);
        });

        console.log('转换完成，总共', convertedData.paths.length, '条路径');
        console.log('转换后的数据:', convertedData);

        return convertedData;
    },

    // 根据状态获取路径类型
    getPathTypeFromStatus(status) {
        const typeMap = {
            'opt0': 'original',
            'opt1': 'predicted'
        };
        return typeMap[status] || 'unknown';
    },

    // 清除缓存
    clearCache() {
        this.cachedSamples = [];
        this.cachedSampleData.clear();
    }
};

// 路径管理器
const pathManager = {
    currentSample: null,
    currentPath: null,
    selectedPoint: null,
    pathObjects: [], // 存储3D路径对象

    // 设置当前样本
    async setCurrentSample(sampleId) {
        try {
            showNotification('正在加载样本数据...', 'info');
            const sampleData = await dataManager.getSampleData(sampleId);

            if (sampleData) {
                this.currentSample = sampleData;
                this.currentPath = null;
                this.selectedPoint = null;
                this.clearScene();

                // 保存状态到localStorage
                stateManager.saveCurrentSample(sampleId, sampleData);

                // 根据样本数据动态定位相机
                this.adjustCameraToSampleData(sampleData);

                this.renderAllPaths();
                this.updateUI();
                showNotification(`样本 "${sampleData.name}" 加载成功`, 'success');

                // 添加操作历史
                addOperationHistory('加载样本', `加载样本 "${sampleData.name}"`);

                // 更新统计信息
                updateStatistics();
            } else {
                throw new Error('样本数据为空');
            }
        } catch (error) {
            console.error('设置当前样本失败:', error);
            showNotification('加载样本数据失败: ' + error.message, 'error');
        }
    },

    // 从保存的状态恢复样本
    async restoreFromSavedState() {
        const savedState = stateManager.loadCurrentSample();
        if (savedState && savedState.sampleData) {
            try {
                this.currentSample = savedState.sampleData;
                this.currentPath = null;
                this.selectedPoint = null;
                this.clearScene();

                // 根据样本数据动态定位相机
                this.adjustCameraToSampleData(savedState.sampleData);

                this.renderAllPaths();
                this.updateUI();
                showNotification(`已恢复样本 "${savedState.sampleData.name}"`, 'success');

                // 添加操作历史
                addOperationHistory('恢复样本', `恢复样本 "${savedState.sampleData.name}"`);

                // 更新统计信息
                updateStatistics();
                return true;
            } catch (error) {
                console.error('恢复样本状态失败:', error);
                stateManager.clearCurrentSample();
                return false;
            }
        }
        return false;
    },

    // 获取当前样本
    getCurrentSample() {
        return this.currentSample;
    },

    // 获取当前样本的所有路径
    getCurrentPaths() {
        return this.currentSample ? this.currentSample.paths : [];
    },

    // 获取可见路径
    getVisiblePaths() {
        return this.getCurrentPaths().filter(path => path.visible);
    },

    // 切换路径可见性
    togglePathVisibility(pathId) {
        const paths = this.getCurrentPaths();
        const path = paths.find(p => p.id === pathId);
        if (path) {
            path.visible = !path.visible;
            this.renderAllPaths();
            this.updateUI();
            addOperationHistory('切换路径可见性', `${path.visible ? '显示' : '隐藏'}路径 "${path.name}"`);
        }
    },

    // 设置当前编辑路径
    setCurrentPath(pathId) {
        const paths = this.getCurrentPaths();
        const path = paths.find(p => p.id === pathId);
        if (path) {
            this.currentPath = path;
            this.selectedPoint = null;
            this.updateUI();
            addOperationHistory('选择路径', `选择路径 "${path.name}" 进行编辑`);
        }
    },

    // 清空3D场景
    clearScene() {
        this.pathObjects.forEach(obj => {
            if (obj.points) {
                obj.points.forEach(pointObj => {
                    scene.remove(pointObj.mesh);
                    if (pointObj.directionIndicator) {
                        scene.remove(pointObj.directionIndicator);
                    }
                });
            }
            if (obj.lines) {
                obj.lines.forEach(line => scene.remove(line));
            }
        });
        this.pathObjects = [];
    },

    // 渲染所有路径
    renderAllPaths() {
        if (viewMode === '3d') {
            this.clearScene();

            const visiblePaths = this.getVisiblePaths();
            visiblePaths.forEach(path => {
                this.renderPath(path);
            });
        } else if (viewMode === '2d') {
            // 在2D模式下，使用canvas2DManager渲染
            canvas2DManager.render();
        }
    },

    // 渲染单条路径
    renderPath(path) {
        console.log(`渲染路径: ${path.name} (${path.color}), 包含 ${path.points.length} 个点`);

        const pathObj = {
            id: path.id,
            points: [],
            lines: []
        };

        // 渲染路径点
        path.points.forEach((point) => {
            console.log(`  点 ${point.id}: (${point.x}, ${point.y}, ${point.z}), 角度: ${point.angle}°, 可编辑: ${point.flag === 1 ? '是' : '否'}`);
            const pointObj = this.createPoint(point, path.color);
            pathObj.points.push(pointObj);
        });

        // 渲染连接线
        if (path.points.length > 1) {
            for (let i = 0; i < path.points.length - 1; i++) {
                const line = this.createLine(path.points[i], path.points[i + 1], path.color);
                pathObj.lines.push(line);
                console.log(`  连线: 点${path.points[i].id} -> 点${path.points[i + 1].id} (颜色: ${path.color})`);
            }
        }

        this.pathObjects.push(pathObj);
        console.log(`路径 ${path.name} 渲染完成`);
    },

    // 创建3D点
    createPoint(point, pathColor) {
        const geometry = new THREE.SphereGeometry(pointSize, 32, 32);
        // 根据flag决定点的颜色：flag=1使用路径颜色，flag=0使用灰色
        const pointColor = point.flag === 1 ? pathColor : '#cccccc';
        const material = new THREE.MeshBasicMaterial({
            color: pointColor,
            transparent: true,
            opacity: pathOpacity
        });
        const mesh = new THREE.Mesh(geometry, material);

        // 使用实际的x,y,z坐标
        mesh.position.set(point.x, point.y, point.z || 0);
        mesh.userData = { point: point, pathColor: pathColor };
        scene.add(mesh);

        // 创建方向指示器
        const directionIndicator = this.createDirectionIndicator(point, pathColor);

        return {
            mesh: mesh,
            directionIndicator: directionIndicator,
            point: point
        };
    },

    // 创建方向指示器
    createDirectionIndicator(point, pathColor) {
        const arrowLength = 0.4;
        const geometry = new THREE.ConeGeometry(0.08, arrowLength, 8);
        // 角度指示器颜色与所在点颜色一致：flag=1使用路径颜色，flag=0使用灰色
        const arrowColor = point.flag === 1 ? pathColor : '#aaaaaa';
        const material = new THREE.MeshBasicMaterial({ color: arrowColor });
        const indicator = new THREE.Mesh(geometry, material);

        // 设置位置和角度
        const angleRad = (point.angle - 90) * (Math.PI / 180);
        indicator.rotation.z = angleRad;
        indicator.position.set(point.x, point.y, point.z || 0);
        indicator.position.x += Math.cos(angleRad) * (arrowLength / 2 + 0.05);
        indicator.position.y += Math.sin(angleRad) * (arrowLength / 2 + 0.05);

        scene.add(indicator);
        return indicator;
    },

    // 创建连接线
    createLine(point1, point2, pathColor) {
        const start = new THREE.Vector3(point1.x, point1.y, point1.z || 0);
        const end = new THREE.Vector3(point2.x, point2.y, point2.z || 0);

        // 创建路径曲线
        const curve = new THREE.LineCurve3(start, end);

        // 使用TubeGeometry创建有粗细的线条
        const tubeGeometry = new THREE.TubeGeometry(curve, 1, pathThickness * 0.1, 8, false);
        const material = new THREE.MeshBasicMaterial({
            color: pathColor,
            transparent: true,
            opacity: pathOpacity
        });

        const line = new THREE.Mesh(tubeGeometry, material);
        line.userData.isPathLine = true;
        scene.add(line);
        return line;
    },

    // 根据样本数据调整相机位置
    adjustCameraToSampleData(sampleData) {
        if (!sampleData || !sampleData.paths || sampleData.paths.length === 0) {
            return;
        }

        let minX = Infinity, maxX = -Infinity;
        let minY = Infinity, maxY = -Infinity;
        let minZ = Infinity, maxZ = -Infinity;

        // 计算所有路径点的边界
        sampleData.paths.forEach(path => {
            path.points.forEach(point => {
                minX = Math.min(minX, point.x);
                maxX = Math.max(maxX, point.x);
                minY = Math.min(minY, point.y);
                maxY = Math.max(maxY, point.y);
                minZ = Math.min(minZ, point.z || 0);
                maxZ = Math.max(maxZ, point.z || 0);
            });
        });

        // 计算中心点和范围
        const centerX = (minX + maxX) / 2;
        const centerY = (minY + maxY) / 2;
        const centerZ = (minZ + maxZ) / 2;
        const rangeX = maxX - minX;
        const rangeY = maxY - minY;
        const rangeZ = maxZ - minZ;

        // 计算合适的相机距离
        const maxRange = Math.max(rangeX, rangeY, rangeZ);
        const distance = Math.max(maxRange * 2, 10);

        // 设置相机位置
        camera.position.set(centerX + distance, centerY + distance, centerZ + distance);
        camera.lookAt(centerX, centerY, centerZ);

        // 更新控制器目标
        if (controls) {
            controls.target.set(centerX, centerY, centerZ);
            controls.update();
        }

        console.log(`相机已调整到样本数据中心: (${centerX.toFixed(2)}, ${centerY.toFixed(2)}, ${centerZ.toFixed(2)})`);
    },

    // 更新UI
    updateUI() {
        this.updatePathsList();
        this.updateLayerSelector();
        this.updateSampleInfo();
    },

    // 更新路径列表
    updatePathsList() {
        const pointsContainer = document.getElementById('points-container');
        const pointsCount = document.getElementById('points-count');

        if (!pointsContainer || !pointsCount) return;

        if (!this.currentSample) {
            pointsContainer.innerHTML = `
                <div class="no-sample-message">
                    <div class="no-sample-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3>未选择样本</h3>
                    <p>请点击"样本选择"按钮选择路径数据</p>
                    <button class="btn-primary-small" onclick="showSampleSelectionModal()">
                        <i class="fas fa-plus"></i>选择样本
                    </button>
                </div>
            `;
            pointsCount.textContent = '0 条路径';
            return;
        }

        const paths = this.getCurrentPaths();
        pointsCount.textContent = `${paths.length} 条路径`;

        pointsContainer.innerHTML = '';

        // 添加样本信息头部
        const sampleHeader = document.createElement('div');
        sampleHeader.className = 'sample-header';
        sampleHeader.innerHTML = `
            <div class="sample-info">
                <div class="sample-title">
                    <i class="fas fa-route"></i>
                    <span>${this.currentSample.name}</span>
                </div>
                <div class="sample-meta">
                    <span class="sample-type">${this.getSampleTypeLabel(this.currentSample.type)}</span>
                    <span class="sample-difficulty difficulty-${this.currentSample.difficulty}">
                        ${this.getDifficultyLabel(this.currentSample.difficulty)}
                    </span>
                </div>
                <p class="sample-description">${this.currentSample.description}</p>
            </div>
            <button class="change-sample-btn" onclick="showSampleSelectionModal()" title="更换样本">
                <i class="fas fa-exchange-alt"></i>
            </button>
        `;
        pointsContainer.appendChild(sampleHeader);

        // 添加路径列表
        const pathsList = document.createElement('div');
        pathsList.className = 'paths-list';

        paths.forEach((path, index) => {
            const pathElement = document.createElement('div');
            pathElement.className = `path-item ${this.currentPath && this.currentPath.id === path.id ? 'active' : ''} ${path.visible ? 'visible' : 'hidden'}`;

            // 计算可编辑点数量
            const editablePoints = path.points.filter(p => p.flag === 1).length;
            const totalPoints = path.points.length;

            pathElement.innerHTML = `
                <div class="path-header">
                    <div class="path-indicator" style="background: ${path.color}"></div>
                    <div class="path-info">
                        <div class="path-name-row">
                            <span class="path-name">${path.name}</span>
                        </div>
                        <div class="path-details">
                            <span class="path-type">${this.getPathTypeLabel(path.type)}</span>
                            <span class="path-points">
                                <i class="fas fa-map-marker-alt"></i>
                                ${totalPoints} 点 (${editablePoints} 可编辑)
                            </span>
                        </div>
                    </div>
                    <div class="path-actions">
                        <button class="path-action-btn visibility-btn ${path.visible ? 'active' : ''}"
                                onclick="pathManager.togglePathVisibility('${path.id}')"
                                title="${path.visible ? '隐藏路径' : '显示路径'}">
                            <i class="fas fa-${path.visible ? 'eye' : 'eye-slash'}"></i>
                        </button>
                        <button class="path-action-btn edit-btn"
                                onclick="pathManager.setCurrentPath('${path.id}')"
                                title="编辑路径">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${path.video ? `
                            <button class="path-action-btn video-btn"
                                    onclick="showVideoModal('${path.id}')"
                                    title="查看视频">
                                <i class="fas fa-play"></i>
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;

            pathElement.addEventListener('click', (e) => {
                if (!e.target.closest('.path-actions')) {
                    pathManager.setCurrentPath(path.id);
                }
            });

            pathsList.appendChild(pathElement);
        });

        pointsContainer.appendChild(pathsList);
    },

    // 更新图层选择器
    updateLayerSelector() {
        const layerSelect = document.getElementById('layer-select');
        const layerColor = document.getElementById('layer-color');

        if (!layerSelect || !layerColor) return;

        // 清空现有选项
        layerSelect.innerHTML = '';

        // 添加路径选项
        const paths = this.getCurrentPaths();
        paths.forEach(path => {
            const option = document.createElement('option');
            option.value = path.id;
            option.textContent = path.name;
            option.dataset.color = path.color;
            layerSelect.appendChild(option);
        });

        // 添加"显示全部"选项
        const allOption = document.createElement('option');
        allOption.value = 'all';
        allOption.textContent = '显示全部';
        allOption.dataset.color = '#ffffff';
        layerSelect.appendChild(allOption);

        // 更新颜色指示器
        const selectedOption = layerSelect.options[layerSelect.selectedIndex];
        if (selectedOption) {
            layerColor.style.backgroundColor = selectedOption.dataset.color;
        }
    },

    // 更新样本信息
    updateSampleInfo() {
        const selectedSamples = document.getElementById('selected-samples');
        if (selectedSamples) {
            selectedSamples.textContent = this.currentSample ? '1' : '0';
        }
    },

    // 获取样本类型标签
    getSampleTypeLabel(type) {
        const typeMap = {
            'api_sample': 'API样本',
            'local_sample': '本地样本',
            'generated': '生成样本'
        };
        return typeMap[type] || '未知类型';
    },

    // 获取难度标签
    getDifficultyLabel(difficulty) {
        const difficultyMap = {
            'easy': '简单',
            'medium': '中等',
            'hard': '困难'
        };
        return difficultyMap[difficulty] || '未知';
    },

    // 获取路径类型标签
    getPathTypeLabel(type) {
        const typeMap = {
            'original': '原始',
            'predicted': '预测',
            'optimized': '优化',
            'unknown': '未知'
        };
        return typeMap[type] || '未知';
    }
};

// 通知系统
function showNotification(message, type = 'success') {
    const notificationId = `${type}-notification`;
    const notification = document.getElementById(notificationId);

    if (notification) {
        const content = notification.querySelector('.notification-content p');
        if (content) {
            const icon = type === 'success' ? 'check-circle' :
                        type === 'error' ? 'exclamation-circle' : 'info-circle';
            content.innerHTML = `<i class="fas fa-${icon}"></i> ${message}`;
        }

        notification.style.display = 'block';
        notification.classList.add('show');

        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.style.display = 'none';
            }, 300);
        }, 3000);
    }
}

// 操作历史管理
function addOperationHistory(operation, description) {
    const timestamp = new Date().toLocaleTimeString();
    operationHistory.unshift({
        operation,
        description,
        timestamp
    });

    // 限制历史记录数量
    if (operationHistory.length > 10) {
        operationHistory = operationHistory.slice(0, 10);
    }

    updateOperationHistoryUI();
}

function updateOperationHistoryUI() {
    const activityList = document.getElementById('activity-list');
    if (!activityList) return;

    activityList.innerHTML = '';

    if (operationHistory.length === 0) {
        activityList.innerHTML = `
            <div class="activity-item">
                <div class="activity-icon info">
                    <i class="fas fa-info"></i>
                </div>
                <div class="activity-content">
                    <span class="activity-text">暂无操作记录</span>
                    <span class="activity-time">-</span>
                </div>
            </div>
        `;
        return;
    }

    operationHistory.forEach(record => {
        const activityItem = document.createElement('div');
        activityItem.className = 'activity-item';

        const iconClass = record.operation.includes('加载') ? 'success' :
                         record.operation.includes('编辑') ? 'warning' :
                         record.operation.includes('删除') ? 'danger' : 'info';

        const iconName = record.operation.includes('加载') ? 'download' :
                        record.operation.includes('编辑') ? 'edit' :
                        record.operation.includes('删除') ? 'trash' :
                        record.operation.includes('保存') ? 'save' : 'info';

        activityItem.innerHTML = `
            <div class="activity-icon ${iconClass}">
                <i class="fas fa-${iconName}"></i>
            </div>
            <div class="activity-content">
                <span class="activity-text">${record.description}</span>
                <span class="activity-time">${record.timestamp}</span>
            </div>
        `;

        activityList.appendChild(activityItem);
    });
}

function clearOperationHistory() {
    operationHistory = [];
    updateOperationHistoryUI();
    showNotification('操作历史已清空', 'info');
}

// 统计信息更新
function updateStatistics() {
    const currentSample = pathManager.getCurrentSample();
    if (!currentSample) {
        document.getElementById('total-points').textContent = '0';
        document.getElementById('total-length').textContent = '0.0m';
        document.getElementById('average-angle').textContent = '0.0°';
        document.getElementById('estimated-time').textContent = '0.0s';
        return;
    }

    let totalPoints = 0;
    let totalLength = 0;
    let totalAngle = 0;
    let angleCount = 0;

    currentSample.paths.forEach(path => {
        totalPoints += path.points.length;

        // 计算路径长度
        for (let i = 0; i < path.points.length - 1; i++) {
            const p1 = path.points[i];
            const p2 = path.points[i + 1];
            const distance = Math.sqrt(
                Math.pow(p2.x - p1.x, 2) +
                Math.pow(p2.y - p1.y, 2) +
                Math.pow((p2.z || 0) - (p1.z || 0), 2)
            );
            totalLength += distance;
        }

        // 计算平均角度
        path.points.forEach(point => {
            if (point.angle !== undefined) {
                totalAngle += point.angle;
                angleCount++;
            }
        });
    });

    const averageAngle = angleCount > 0 ? totalAngle / angleCount : 0;
    const estimatedTime = totalLength * 0.1; // 假设每单位距离需要0.1秒

    document.getElementById('total-points').textContent = totalPoints.toString();
    document.getElementById('total-length').textContent = `${totalLength.toFixed(1)}m`;
    document.getElementById('average-angle').textContent = `${averageAngle.toFixed(1)}°`;
    document.getElementById('estimated-time').textContent = `${estimatedTime.toFixed(1)}s`;
}

// 2D Canvas管理器
const canvas2DManager = {
    canvas: null,
    ctx: null,
    scale: 1,
    offset: { x: 0, y: 0 },
    selectedPoint: null,

    // 初始化2D Canvas
    init() {
        this.canvas = document.getElementById('canvas-2d');
        if (!this.canvas) {
            console.error('找不到2D Canvas元素');
            return;
        }

        this.ctx = this.canvas.getContext('2d');
        this.resizeCanvas();

        // 添加事件监听
        this.canvas.addEventListener('click', this.onCanvasClick.bind(this));
        this.canvas.addEventListener('mousemove', this.onCanvasMouseMove.bind(this));
        window.addEventListener('resize', this.resizeCanvas.bind(this));

        // 初始化控制按钮
        this.initControls();

        console.log('2D Canvas初始化完成');
    },

    // 调整Canvas大小
    resizeCanvas() {
        if (!this.canvas) return;

        const container = this.canvas.parentElement;
        const rect = container.getBoundingClientRect();

        this.canvas.width = rect.width;
        this.canvas.height = rect.height;

        // 重新渲染
        if (viewMode === '2d') {
            this.render();
        }
    },

    // 初始化控制按钮
    initControls() {
        const zoomInBtn = document.getElementById('zoom-in-2d');
        const zoomOutBtn = document.getElementById('zoom-out-2d');
        const resetZoomBtn = document.getElementById('reset-zoom-2d');

        if (zoomInBtn) {
            zoomInBtn.addEventListener('click', () => this.zoomIn());
        }
        if (zoomOutBtn) {
            zoomOutBtn.addEventListener('click', () => this.zoomOut());
        }
        if (resetZoomBtn) {
            resetZoomBtn.addEventListener('click', () => this.resetZoom());
        }
    },

    // 放大
    zoomIn() {
        this.scale = Math.min(this.scale * 1.2, 5);
        this.updateZoomDisplay();
        this.render();
    },

    // 缩小
    zoomOut() {
        this.scale = Math.max(this.scale / 1.2, 0.1);
        this.updateZoomDisplay();
        this.render();
    },

    // 重置缩放
    resetZoom() {
        this.scale = 1;
        this.offset = { x: 0, y: 0 };
        this.updateZoomDisplay();
        this.render();
    },

    // 更新缩放显示
    updateZoomDisplay() {
        const zoomLevel = document.getElementById('zoom-level-2d');
        const zoomDisplay = document.getElementById('zoom-display-2d');

        const zoomPercent = Math.round(this.scale * 100) + '%';

        if (zoomLevel) zoomLevel.textContent = zoomPercent;
        if (zoomDisplay) zoomDisplay.textContent = zoomPercent;
    },

    // Canvas点击事件
    onCanvasClick(event) {
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // 转换为世界坐标
        const worldX = (x - this.canvas.width / 2 - this.offset.x) / this.scale;
        const worldY = (this.canvas.height / 2 - y - this.offset.y) / this.scale;

        // 检查是否点击了路径点
        const clickedPoint = this.getPointAtPosition(worldX, worldY);

        if (clickedPoint) {
            this.selectedPoint = clickedPoint;
            this.updateSelectedPointDisplay();
            this.render();

            // 显示2D点编辑模态框
            showPoint2DEditModal(clickedPoint);
        } else {
            this.selectedPoint = null;
            this.updateSelectedPointDisplay();
            this.render();
        }
    },

    // Canvas鼠标移动事件
    onCanvasMouseMove(event) {
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // 转换为世界坐标
        const worldX = (x - this.canvas.width / 2 - this.offset.x) / this.scale;
        const worldY = (this.canvas.height / 2 - y - this.offset.y) / this.scale;

        // 更新鼠标位置显示
        const mousePos = document.getElementById('mouse-position-2d');
        if (mousePos) {
            mousePos.textContent = `(${worldX.toFixed(1)}, ${worldY.toFixed(1)})`;
        }
    },

    // 获取指定位置的路径点
    getPointAtPosition(x, y) {
        const currentSample = pathManager.getCurrentSample();
        if (!currentSample) return null;

        const tolerance = 10 / this.scale; // 点击容差

        for (const path of currentSample.paths) {
            if (!path.visible) continue;

            for (const point of path.points) {
                const distance = Math.sqrt(
                    Math.pow(point.x - x, 2) + Math.pow(point.y - y, 2)
                );

                if (distance <= tolerance) {
                    return point;
                }
            }
        }

        return null;
    },

    // 更新选中点显示
    updateSelectedPointDisplay() {
        const selectedPointDisplay = document.getElementById('selected-point-2d');
        if (selectedPointDisplay) {
            if (this.selectedPoint) {
                selectedPointDisplay.textContent = `ID: ${this.selectedPoint.id}`;
            } else {
                selectedPointDisplay.textContent = '无';
            }
        }
    },

    // 渲染2D场景
    render() {
        if (!this.ctx) return;

        const { width, height } = this.canvas;

        // 清空画布
        this.ctx.clearRect(0, 0, width, height);

        // 设置背景
        this.ctx.fillStyle = '#0a0a0a';
        this.ctx.fillRect(0, 0, width, height);

        // 保存当前状态
        this.ctx.save();

        // 应用变换
        this.ctx.translate(width / 2 + this.offset.x, height / 2 + this.offset.y);
        this.ctx.scale(this.scale, -this.scale); // Y轴翻转以匹配数学坐标系

        // 绘制网格
        this.drawGrid();

        // 绘制坐标轴
        this.drawAxes();

        // 绘制路径
        this.drawPaths();

        // 恢复状态
        this.ctx.restore();
    },

    // 绘制网格
    drawGrid() {
        const gridSize = 50;
        const { width, height } = this.canvas;
        const halfWidth = width / 2 / this.scale;
        const halfHeight = height / 2 / this.scale;

        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1 / this.scale;

        this.ctx.beginPath();

        // 垂直线
        for (let x = -Math.ceil(halfWidth / gridSize) * gridSize; x <= halfWidth; x += gridSize) {
            this.ctx.moveTo(x, -halfHeight);
            this.ctx.lineTo(x, halfHeight);
        }

        // 水平线
        for (let y = -Math.ceil(halfHeight / gridSize) * gridSize; y <= halfHeight; y += gridSize) {
            this.ctx.moveTo(-halfWidth, y);
            this.ctx.lineTo(halfWidth, y);
        }

        this.ctx.stroke();
    },

    // 绘制坐标轴
    drawAxes() {
        const { width, height } = this.canvas;
        const halfWidth = width / 2 / this.scale;
        const halfHeight = height / 2 / this.scale;

        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        this.ctx.lineWidth = 2 / this.scale;

        this.ctx.beginPath();
        // X轴
        this.ctx.moveTo(-halfWidth, 0);
        this.ctx.lineTo(halfWidth, 0);
        // Y轴
        this.ctx.moveTo(0, -halfHeight);
        this.ctx.lineTo(0, halfHeight);
        this.ctx.stroke();
    },

    // 绘制路径
    drawPaths() {
        const currentSample = pathManager.getCurrentSample();
        if (!currentSample) return;

        const visiblePaths = currentSample.paths.filter(path => path.visible);

        visiblePaths.forEach(path => {
            this.drawPath(path);
        });
    },

    // 绘制单条路径
    drawPath(path) {
        if (path.points.length === 0) return;

        // 绘制连接线
        if (path.points.length > 1) {
            this.ctx.strokeStyle = path.color;
            this.ctx.lineWidth = pathThickness / this.scale;
            this.ctx.lineCap = 'round';
            this.ctx.lineJoin = 'round';

            this.ctx.beginPath();
            this.ctx.moveTo(path.points[0].x, path.points[0].y);

            for (let i = 1; i < path.points.length; i++) {
                this.ctx.lineTo(path.points[i].x, path.points[i].y);
            }

            this.ctx.stroke();
        }

        // 绘制路径点
        path.points.forEach(point => {
            this.drawPoint(point, path.color);
        });
    },

    // 绘制路径点
    drawPoint(point, pathColor) {
        const radius = 8 / this.scale;
        const isSelected = this.selectedPoint && this.selectedPoint.id === point.id;

        // 绘制点
        this.ctx.fillStyle = point.flag === 1 ? pathColor : '#cccccc';
        if (isSelected) {
            this.ctx.fillStyle = '#ffff00'; // 选中时高亮
        }

        this.ctx.beginPath();
        this.ctx.arc(point.x, point.y, radius, 0, Math.PI * 2);
        this.ctx.fill();

        // 绘制边框
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
        this.ctx.lineWidth = 1 / this.scale;
        this.ctx.stroke();

        // 绘制方向指示器
        if (point.angle !== undefined) {
            this.drawDirectionIndicator(point, pathColor);
        }
    },

    // 绘制方向指示器
    drawDirectionIndicator(point, pathColor) {
        const arrowLength = 20 / this.scale;
        const arrowWidth = 6 / this.scale;

        const angleRad = (point.angle - 90) * (Math.PI / 180);
        const endX = point.x + Math.cos(angleRad) * arrowLength;
        const endY = point.y + Math.sin(angleRad) * arrowLength;

        // 绘制箭头线
        this.ctx.strokeStyle = point.flag === 1 ? pathColor : '#aaaaaa';
        this.ctx.lineWidth = 2 / this.scale;
        this.ctx.lineCap = 'round';

        this.ctx.beginPath();
        this.ctx.moveTo(point.x, point.y);
        this.ctx.lineTo(endX, endY);
        this.ctx.stroke();

        // 绘制箭头头部
        const headLength = 8 / this.scale;
        const headAngle = Math.PI / 6;

        this.ctx.beginPath();
        this.ctx.moveTo(endX, endY);
        this.ctx.lineTo(
            endX - headLength * Math.cos(angleRad - headAngle),
            endY - headLength * Math.sin(angleRad - headAngle)
        );
        this.ctx.moveTo(endX, endY);
        this.ctx.lineTo(
            endX - headLength * Math.cos(angleRad + headAngle),
            endY - headLength * Math.sin(angleRad + headAngle)
        );
        this.ctx.stroke();
    }
};

// 视图模式管理器
const viewModeManager = {
    currentMode: '3d',

    // 初始化视图模式切换
    init() {
        const tab3D = document.getElementById('tab-3d');
        const tab2D = document.getElementById('tab-2d');

        if (tab3D) {
            tab3D.addEventListener('click', () => this.switchTo3D());
        }
        if (tab2D) {
            tab2D.addEventListener('click', () => this.switchTo2D());
        }

        console.log('视图模式管理器初始化完成');
    },

    // 切换到3D视图
    switchTo3D() {
        if (this.currentMode === '3d') return;

        this.currentMode = '3d';
        viewMode = '3d';

        // 更新标签页状态
        this.updateTabStates();

        // 切换场景显示
        this.switchSceneDisplay();

        addOperationHistory('切换视图', '切换到3D视图');
        showNotification('已切换到3D视图', 'info');
    },

    // 切换到2D视图
    switchTo2D() {
        if (this.currentMode === '2d') return;

        this.currentMode = '2d';
        viewMode = '2d';

        // 更新标签页状态
        this.updateTabStates();

        // 切换场景显示
        this.switchSceneDisplay();

        // 渲染2D场景
        canvas2DManager.render();

        addOperationHistory('切换视图', '切换到2D视图');
        showNotification('已切换到2D视图', 'info');
    },

    // 更新标签页状态
    updateTabStates() {
        const tab3D = document.getElementById('tab-3d');
        const tab2D = document.getElementById('tab-2d');

        if (tab3D && tab2D) {
            tab3D.classList.toggle('active', this.currentMode === '3d');
            tab2D.classList.toggle('active', this.currentMode === '2d');
        }
    },

    // 切换场景显示
    switchSceneDisplay() {
        const scene3D = document.getElementById('processed-video-container');
        const scene2D = document.getElementById('canvas-2d-container');

        if (scene3D && scene2D) {
            scene3D.classList.toggle('active', this.currentMode === '3d');
            scene2D.classList.toggle('active', this.currentMode === '2d');
        }
    }
};

// 模态框管理
function showSampleSelectionModal() {
    const modal = document.getElementById('sample-modal');
    if (modal) {
        modal.style.display = 'flex';
        loadSampleData();
    }
}

function closeSampleModal() {
    const modal = document.getElementById('sample-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function showPointEditModal(point) {
    const modal = document.getElementById('point-edit-modal');
    if (modal && point) {
        // 填充表单数据
        document.getElementById('point-x').value = point.x;
        document.getElementById('point-y').value = point.y;
        document.getElementById('point-z').value = point.z || 0;
        document.getElementById('point-angle').value = point.angle || 0;
        document.getElementById('point-flag').value = point.flag || 0;

        modal.style.display = 'flex';
    }
}

function closePointEditModal() {
    const modal = document.getElementById('point-edit-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function showPoint2DEditModal(point) {
    const modal = document.getElementById('point-edit-2d-modal');
    if (modal && point) {
        // 填充表单数据
        document.getElementById('point-2d-x').value = point.x;
        document.getElementById('point-2d-y').value = point.y;
        document.getElementById('point-2d-angle').value = point.angle || 0;
        document.getElementById('point-2d-id').value = point.id;
        document.getElementById('point-2d-flag').value = point.flag || 0;

        // 同步角度滑块
        const angleSlider = document.getElementById('angle-2d-slider');
        if (angleSlider) {
            angleSlider.value = point.angle || 0;
        }

        // 更新角度预览
        updateAnglePreview2D(point.angle || 0);

        modal.style.display = 'flex';

        // 绑定表单提交事件
        const form = document.getElementById('point-edit-2d-form');
        if (form) {
            form.onsubmit = (e) => {
                e.preventDefault();
                savePoint2DChanges(point);
            };
        }

        // 绑定删除按钮事件
        const deleteBtn = document.getElementById('delete-point-2d');
        if (deleteBtn) {
            deleteBtn.onclick = () => deletePoint2D(point);
        }

        // 绑定角度输入和滑块同步
        const angleInput = document.getElementById('point-2d-angle');
        if (angleInput && angleSlider) {
            angleInput.addEventListener('input', (e) => {
                const value = parseFloat(e.target.value) || 0;
                angleSlider.value = value;
                updateAnglePreview2D(value);
            });

            angleSlider.addEventListener('input', (e) => {
                const value = parseFloat(e.target.value) || 0;
                angleInput.value = value;
                updateAnglePreview2D(value);
            });
        }
    }
}

function closePoint2DEditModal() {
    const modal = document.getElementById('point-edit-2d-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function updateAnglePreview2D(angle) {
    const angleValue = document.getElementById('angle-value-2d');
    const angleArrow = document.getElementById('angle-arrow-2d');

    if (angleValue) {
        angleValue.textContent = `${angle}°`;
    }

    if (angleArrow) {
        angleArrow.style.transform = `rotate(${angle}deg)`;
    }
}

function savePoint2DChanges(point) {
    try {
        // 获取表单数据
        const newX = parseFloat(document.getElementById('point-2d-x').value);
        const newY = parseFloat(document.getElementById('point-2d-y').value);
        const newAngle = parseFloat(document.getElementById('point-2d-angle').value) || 0;
        const newFlag = parseInt(document.getElementById('point-2d-flag').value) || 0;

        // 验证数据
        if (isNaN(newX) || isNaN(newY)) {
            showNotification('请输入有效的坐标值', 'error');
            return;
        }

        // 更新点数据
        const oldX = point.x;
        const oldY = point.y;
        const oldAngle = point.angle;

        point.x = newX;
        point.y = newY;
        point.angle = newAngle;
        point.flag = newFlag;

        // 重新渲染场景
        if (viewMode === '2d') {
            canvas2DManager.render();
        } else {
            pathManager.renderAllPaths();
        }

        // 更新UI
        pathManager.updateUI();
        updateStatistics();

        // 关闭模态框
        closePoint2DEditModal();

        // 添加操作历史
        addOperationHistory('编辑点',
            `修改点 ${point.id}: 坐标从(${oldX.toFixed(1)}, ${oldY.toFixed(1)})改为(${newX.toFixed(1)}, ${newY.toFixed(1)}), 角度从${oldAngle}°改为${newAngle}°`);

        showNotification('路径点已更新', 'success');

    } catch (error) {
        console.error('保存2D点更改失败:', error);
        showNotification('保存失败: ' + error.message, 'error');
    }
}

function deletePoint2D(point) {
    if (!point) return;

    if (point.flag !== 1) {
        showNotification('该路径点已锁定，无法删除', 'warning');
        return;
    }

    if (!confirm(`确定要删除点 ${point.id} 吗？`)) {
        return;
    }

    try {
        // 从当前路径中删除点
        const currentSample = pathManager.getCurrentSample();
        if (currentSample) {
            let pointDeleted = false;

            currentSample.paths.forEach(path => {
                const pointIndex = path.points.findIndex(p => p.id === point.id);
                if (pointIndex !== -1) {
                    path.points.splice(pointIndex, 1);
                    pointDeleted = true;
                }
            });

            if (pointDeleted) {
                // 清除选中状态
                canvas2DManager.selectedPoint = null;
                canvas2DManager.updateSelectedPointDisplay();

                // 重新渲染场景
                if (viewMode === '2d') {
                    canvas2DManager.render();
                } else {
                    pathManager.renderAllPaths();
                }

                // 更新UI
                pathManager.updateUI();
                updateStatistics();

                // 关闭模态框
                closePoint2DEditModal();

                // 添加操作历史
                addOperationHistory('删除点', `删除点 ${point.id}`);

                showNotification('路径点已删除', 'success');
            } else {
                showNotification('未找到要删除的点', 'error');
            }
        }

    } catch (error) {
        console.error('删除2D点失败:', error);
        showNotification('删除失败: ' + error.message, 'error');
    }
}

function showOperationGuide() {
    const modal = document.getElementById('operation-guide-modal');
    if (modal) {
        modal.style.display = 'flex';
    }
}

function closeOperationGuide() {
    const modal = document.getElementById('operation-guide-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function showShortcuts() {
    showNotification('快捷键功能正在开发中...', 'info');
}

// GUI工具栏功能
function initGUIToolbar() {
    // 添加点按钮
    const addPointBtn = document.getElementById('add-point-btn');
    if (addPointBtn) {
        addPointBtn.addEventListener('click', addNewPoint);
    }

    // 删除点按钮
    const deletePointBtn = document.getElementById('delete-point-btn');
    if (deletePointBtn) {
        deletePointBtn.addEventListener('click', deleteSelectedPoint);
    }

    // 编辑点按钮
    const editPointBtn = document.getElementById('edit-point-btn');
    if (editPointBtn) {
        editPointBtn.addEventListener('click', editSelectedPoint);
    }

    // 连接点按钮
    const connectPointsBtn = document.getElementById('connect-points-btn');
    if (connectPointsBtn) {
        connectPointsBtn.addEventListener('click', connectSelectedPoints);
    }

    // 分割路径按钮
    const splitPathBtn = document.getElementById('split-path-btn');
    if (splitPathBtn) {
        splitPathBtn.addEventListener('click', splitPath);
    }

    // 平滑路径按钮
    const smoothPathBtn = document.getElementById('smooth-path-btn');
    if (smoothPathBtn) {
        smoothPathBtn.addEventListener('click', smoothPath);
    }

    // 视图控制按钮
    const resetViewBtn = document.getElementById('reset-view-btn');
    if (resetViewBtn) {
        resetViewBtn.addEventListener('click', resetView);
    }

    const topViewBtn = document.getElementById('top-view-btn');
    if (topViewBtn) {
        topViewBtn.addEventListener('click', setTopView);
    }

    const sideViewBtn = document.getElementById('side-view-btn');
    if (sideViewBtn) {
        sideViewBtn.addEventListener('click', setSideView);
    }

    // 渲染设置滑块
    const pointSizeSlider = document.getElementById('point-size-slider');
    if (pointSizeSlider) {
        pointSizeSlider.addEventListener('input', updatePointSize);
    }

    const lineThicknessSlider = document.getElementById('line-thickness-slider');
    if (lineThicknessSlider) {
        lineThicknessSlider.addEventListener('input', updateLineThickness);
    }

    const opacitySlider = document.getElementById('opacity-slider');
    if (opacitySlider) {
        opacitySlider.addEventListener('input', updateOpacity);
    }

    // 工具栏折叠按钮
    const guiToggleBtn = document.getElementById('gui-toggle-btn');
    if (guiToggleBtn) {
        guiToggleBtn.addEventListener('click', toggleGUIToolbar);
    }

    console.log('GUI工具栏初始化完成');
}

// 添加新路径点
function addNewPoint() {
    if (!pathManager.currentSample || !pathManager.currentPath) {
        showNotification('请先选择路径', 'warning');
        return;
    }

    // 在当前路径的末尾添加新点
    const currentPath = pathManager.currentPath;
    const lastPoint = currentPath.points[currentPath.points.length - 1];

    const newPoint = {
        id: Date.now(), // 临时ID
        x: lastPoint ? lastPoint.x + 5 : 0,
        y: lastPoint ? lastPoint.y + 5 : 0,
        z: lastPoint ? lastPoint.z : 0,
        angle: lastPoint ? lastPoint.angle : 0,
        flag: 1, // 新添加的点默认可编辑
        speed: 30
    };

    currentPath.points.push(newPoint);
    pathManager.renderAllPaths();
    pathManager.updateUI();

    addOperationHistory('添加点', `在路径 "${currentPath.name}" 中添加新点`);
    updateStatistics();
    showNotification('已添加新路径点', 'success');
}

// 删除选中的路径点
function deleteSelectedPoint() {
    if (!selectedPoint) {
        showNotification('请先选择要删除的路径点', 'warning');
        return;
    }

    if (selectedPoint.flag !== 1) {
        showNotification('该路径点已锁定，无法删除', 'warning');
        return;
    }

    // 从当前路径中删除选中的点
    const currentPath = pathManager.currentPath;
    if (currentPath) {
        const pointIndex = currentPath.points.findIndex(p => p.id === selectedPoint.id);
        if (pointIndex !== -1) {
            currentPath.points.splice(pointIndex, 1);
            selectedPoint = null;

            pathManager.renderAllPaths();
            pathManager.updateUI();
            updateGUIButtonStates();
            updateStatusPanel();

            addOperationHistory('删除点', `从路径 "${currentPath.name}" 中删除路径点`);
            updateStatistics();
            showNotification('已删除路径点', 'success');
        }
    }
}

// 编辑选中的路径点
function editSelectedPoint() {
    if (!selectedPoint) {
        showNotification('请先选择要编辑的路径点', 'warning');
        return;
    }

    if (selectedPoint.flag !== 1) {
        showNotification('该路径点已锁定，无法编辑', 'warning');
        return;
    }

    showPointEditModal(selectedPoint);
}

// 连接选中的点
function connectSelectedPoints() {
    showNotification('连接点功能正在开发中...', 'info');
}

// 分割路径
function splitPath() {
    showNotification('分割路径功能正在开发中...', 'info');
}

// 平滑路径
function smoothPath() {
    if (!pathManager.currentPath) {
        showNotification('请先选择路径', 'warning');
        return;
    }

    // 简单的路径平滑算法
    const path = pathManager.currentPath;
    if (path.points.length < 3) {
        showNotification('路径点数量不足，无法平滑', 'warning');
        return;
    }

    // 对中间点进行平滑处理
    for (let i = 1; i < path.points.length - 1; i++) {
        const prevPoint = path.points[i - 1];
        const currentPoint = path.points[i];
        const nextPoint = path.points[i + 1];

        if (currentPoint.flag === 1) { // 只平滑可编辑的点
            currentPoint.x = (prevPoint.x + currentPoint.x + nextPoint.x) / 3;
            currentPoint.y = (prevPoint.y + currentPoint.y + nextPoint.y) / 3;
            currentPoint.z = (prevPoint.z + currentPoint.z + nextPoint.z) / 3;
        }
    }

    pathManager.renderAllPaths();
    addOperationHistory('平滑路径', `平滑路径 "${path.name}"`);
    showNotification('路径已平滑', 'success');
}

// 重置视图
function resetView() {
    if (pathManager.currentSample) {
        pathManager.adjustCameraToSampleData(pathManager.currentSample);
        showNotification('视图已重置', 'info');
    }
}

// 设置俯视图
function setTopView() {
    if (!camera || !controls) return;

    const target = controls.target;
    camera.position.set(target.x, target.y + 50, target.z);
    camera.lookAt(target);
    controls.update();

    showNotification('已切换到俯视图', 'info');
}

// 设置侧视图
function setSideView() {
    if (!camera || !controls) return;

    const target = controls.target;
    camera.position.set(target.x + 50, target.y, target.z);
    camera.lookAt(target);
    controls.update();

    showNotification('已切换到侧视图', 'info');
}

// 更新点大小
function updatePointSize(event) {
    pointSize = parseFloat(event.target.value);
    document.getElementById('point-size-value').textContent = pointSize.toFixed(2);

    // 重新渲染以应用新的点大小
    pathManager.renderAllPaths();
    addOperationHistory('调整点大小', `点大小调整为 ${pointSize.toFixed(2)}`);
}

// 更新线条粗细
function updateLineThickness(event) {
    pathThickness = parseFloat(event.target.value);
    document.getElementById('line-thickness-value').textContent = pathThickness.toFixed(1);

    // 重新渲染以应用新的线条粗细
    pathManager.renderAllPaths();
    addOperationHistory('调整线条粗细', `线条粗细调整为 ${pathThickness.toFixed(1)}`);
}

// 更新透明度
function updateOpacity(event) {
    pathOpacity = parseFloat(event.target.value);
    document.getElementById('opacity-value').textContent = pathOpacity.toFixed(1);

    // 重新渲染以应用新的透明度
    pathManager.renderAllPaths();
    addOperationHistory('调整透明度', `透明度调整为 ${pathOpacity.toFixed(1)}`);
}

// 折叠/展开GUI工具栏
function toggleGUIToolbar() {
    const toolbar = document.getElementById('gui-toolbar');
    const toggleBtn = document.getElementById('gui-toggle-btn');

    if (!toolbar || !toggleBtn) return;

    guiToolbarCollapsed = !guiToolbarCollapsed;

    if (guiToolbarCollapsed) {
        toolbar.classList.add('collapsed');
        toggleBtn.querySelector('i').className = 'fas fa-chevron-right';
        toggleBtn.title = '展开工具栏';
    } else {
        toolbar.classList.remove('collapsed');
        toggleBtn.querySelector('i').className = 'fas fa-chevron-left';
        toggleBtn.title = '折叠工具栏';
    }
}

// 更新GUI按钮状态
function updateGUIButtonStates() {
    const deleteBtn = document.getElementById('delete-point-btn');
    const editBtn = document.getElementById('edit-point-btn');

    if (deleteBtn && editBtn) {
        const hasSelectedPoint = selectedPoint && selectedPoint.flag === 1;
        deleteBtn.disabled = !hasSelectedPoint;
        editBtn.disabled = !hasSelectedPoint;
    }
}

// 更新状态面板
function updateStatusPanel() {
    const selectedPointInfo = document.getElementById('selected-point-info');
    const pointCoordinates = document.getElementById('point-coordinates');
    const pointAngle = document.getElementById('point-angle');
    const editModeStatus = document.getElementById('edit-mode-status');

    if (selectedPoint) {
        selectedPointInfo.textContent = `点 ID: ${selectedPoint.id}`;
        pointCoordinates.textContent = `(${selectedPoint.x.toFixed(1)}, ${selectedPoint.y.toFixed(1)}, ${selectedPoint.z.toFixed(1)})`;
        pointAngle.textContent = `${selectedPoint.angle}°`;
    } else {
        selectedPointInfo.textContent = '无';
        pointCoordinates.textContent = '-';
        pointAngle.textContent = '-';
    }

    if (editModeStatus) {
        const modeMap = {
            'move': '移动',
            'rotate': '旋转',
            'select': '选择'
        };
        editModeStatus.textContent = modeMap[editMode] || '未知';
    }
}

// 样本数据加载
async function loadSampleData() {
    const sampleGrid = document.getElementById('sample-grid');
    if (!sampleGrid) return;

    try {
        sampleGrid.innerHTML = `
            <div class="loading-samples">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                </div>
                <p>正在加载样本数据...</p>
            </div>
        `;

        const samples = await dataManager.getSamples();

        if (samples.length === 0) {
            sampleGrid.innerHTML = `
                <div class="no-samples-message">
                    <div class="no-samples-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3>暂无样本数据</h3>
                    <p>请检查API连接或稍后重试</p>
                </div>
            `;
            return;
        }

        sampleGrid.innerHTML = '';

        samples.forEach(sample => {
            const sampleCard = document.createElement('div');
            sampleCard.className = 'sample-card';
            sampleCard.dataset.sample = sample.id;

            const imageUrl = sample.image ? `${image_host}${sample.image}` : null;

            sampleCard.innerHTML = `
                <div class="sample-preview">
                    ${imageUrl ?
                        `<img src="${imageUrl}" alt="${sample.name}" style="width: 100%; height: 100%; object-fit: cover;">` :
                        `<i class="fas fa-route"></i>`
                    }
                </div>
                <div class="sample-info">
                    <h3>${sample.name}</h3>
                    <p>${sample.remark || '路径规划样本'}</p>
                    <div class="sample-stats">
                        <span><i class="fas fa-route"></i> ID: ${sample.id}</span>
                        <span><i class="fas fa-clock"></i> ${sample.created_at || '未知时间'}</span>
                    </div>
                </div>
                <div class="sample-status">
                    <span class="status-badge status-active">可用</span>
                </div>
            `;

            sampleCard.addEventListener('click', () => {
                // 移除其他选中状态
                document.querySelectorAll('.sample-card').forEach(card => {
                    card.classList.remove('selected');
                });
                // 添加选中状态
                sampleCard.classList.add('selected');

                // 启用确认按钮
                const confirmBtn = document.getElementById('confirm-sample');
                if (confirmBtn) {
                    confirmBtn.disabled = false;
                    confirmBtn.onclick = () => confirmSampleSelection(sample.id);
                }
            });

            sampleGrid.appendChild(sampleCard);
        });

    } catch (error) {
        console.error('加载样本数据失败:', error);
        sampleGrid.innerHTML = `
            <div class="error-message">
                <div class="error-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3>加载失败</h3>
                <p>${error.message}</p>
                <button class="btn-primary-small" onclick="loadSampleData()">
                    <i class="fas fa-refresh"></i>重试
                </button>
            </div>
        `;
    }
}

async function confirmSampleSelection(sampleId) {
    try {
        await pathManager.setCurrentSample(sampleId);
        closeSampleModal();
    } catch (error) {
        showNotification('加载样本失败: ' + error.message, 'error');
    }
}

// 编辑模式设置
function setEditMode(mode) {
    editMode = mode;

    // 更新工具栏按钮状态
    document.querySelectorAll('.toolbar-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    const activeBtn = document.querySelector(`[data-tool="${mode}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }

    // 更新模式指示器
    const modeIndicator = document.getElementById('mode-indicator');
    const currentModeSpan = document.getElementById('current-mode');

    if (modeIndicator && currentModeSpan) {
        const modeInfo = {
            'move': { icon: 'arrows-alt', name: '移动模式', shortcut: '按 M 切换' },
            'rotate': { icon: 'sync-alt', name: '旋转模式', shortcut: '按 R 切换' },
            'select': { icon: 'mouse-pointer', name: '选择模式', shortcut: '按 S 切换' }
        };

        const info = modeInfo[mode] || modeInfo['move'];

        modeIndicator.querySelector('.mode-icon i').className = `fas fa-${info.icon}`;
        modeIndicator.querySelector('.mode-name').textContent = info.name;
        modeIndicator.querySelector('.mode-shortcut').textContent = info.shortcut;
        currentModeSpan.textContent = info.name;
    }

    addOperationHistory('切换模式', `切换到${mode === 'move' ? '移动' : mode === 'rotate' ? '旋转' : '选择'}模式`);
}

// 路径粗细控制
function updatePathThickness(value) {
    pathThickness = parseFloat(value);
    document.getElementById('thickness-value').textContent = value;

    // 重新渲染所有路径以应用新的粗细
    pathManager.renderAllPaths();

    addOperationHistory('调整粗细', `路径粗细调整为 ${value}`);
}

// 3D场景初始化
function initThreeJS() {
    const container = document.getElementById('processed-video-container');
    if (!container) {
        console.error('找不到3D容器元素');
        return;
    }

    // 创建场景
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x0a0a0a);

    // 创建相机
    const aspect = container.clientWidth / container.clientHeight;
    camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);
    camera.position.set(10, 10, 10);

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(container.clientWidth, container.clientHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // 清空容器并添加渲染器
    container.innerHTML = '';
    container.appendChild(renderer.domElement);

    // 创建控制器
    controls = new THREE.OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.enableZoom = true;
    controls.enablePan = true;
    controls.enableRotate = true;

    // 添加光源
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    // 添加网格
    const gridHelper = new THREE.GridHelper(20, 20, 0x444444, 0x222222);
    scene.add(gridHelper);

    // 添加坐标轴
    const axesHelper = new THREE.AxesHelper(5);
    scene.add(axesHelper);

    // 开始渲染循环
    animate();

    // 添加窗口大小调整监听
    window.addEventListener('resize', onWindowResize);

    // 添加鼠标事件监听
    container.addEventListener('mousemove', onMouseMove);
    container.addEventListener('click', onMouseClick);
    container.addEventListener('dblclick', onMouseDoubleClick);

    console.log('3D场景初始化完成');
    addOperationHistory('系统初始化', '3D场景初始化完成');
}

// 动画循环
function animate() {
    requestAnimationFrame(animate);

    if (controls) {
        controls.update();
    }

    if (renderer && scene && camera) {
        renderer.render(scene, camera);
    }

    // 更新性能监控
    updatePerformanceMetrics();
}

// 窗口大小调整
function onWindowResize() {
    const container = document.getElementById('processed-video-container');
    if (!container || !camera || !renderer) return;

    const aspect = container.clientWidth / container.clientHeight;
    camera.aspect = aspect;
    camera.updateProjectionMatrix();

    renderer.setSize(container.clientWidth, container.clientHeight);
}

// 鼠标移动事件
function onMouseMove(event) {
    const container = event.currentTarget;
    const rect = container.getBoundingClientRect();

    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    // 更新光标坐标显示
    const cursorCoords = document.getElementById('cursor-coords');
    if (cursorCoords) {
        cursorCoords.textContent = `坐标: (${mouse.x.toFixed(2)}, ${mouse.y.toFixed(2)})`;
    }
}

// 鼠标点击事件
function onMouseClick(event) {
    if (!scene || !camera) return;

    raycaster.setFromCamera(mouse, camera);
    const intersects = raycaster.intersectObjects(scene.children, true);

    if (intersects.length > 0) {
        const intersect = intersects[0];
        const object = intersect.object;

        // 检查是否点击了路径点
        if (object.userData && object.userData.point) {
            const point = object.userData.point;
            selectedPoint = point;

            console.log('选中路径点:', point);
            addOperationHistory('选择点', `选择路径点 ID: ${point.id}`);

            // 高亮显示选中的点
            highlightSelectedPoint(object);

            // 更新GUI按钮状态和状态面板
            updateGUIButtonStates();
            updateStatusPanel();
        }
    } else {
        // 点击空白区域，取消选择
        selectedPoint = null;
        updateGUIButtonStates();
        updateStatusPanel();
    }
}

// 鼠标双击事件
function onMouseDoubleClick(event) {
    if (selectedPoint) {
        showPointEditModal(selectedPoint);
    }
}

// 高亮选中的点
function highlightSelectedPoint(pointMesh) {
    // 重置所有点的材质
    scene.children.forEach(child => {
        if (child.userData && child.userData.point) {
            const point = child.userData.point;
            const pathColor = child.userData.pathColor;
            const pointColor = point.flag === 1 ? pathColor : '#cccccc';
            child.material.color.setHex(pointColor.replace('#', '0x'));
        }
    });

    // 高亮选中的点
    pointMesh.material.color.setHex(0xffff00); // 黄色高亮
}

// 性能监控更新
function updatePerformanceMetrics() {
    // 模拟性能数据更新
    const fpsValue = document.getElementById('fps-value');
    const fpsBar = document.getElementById('fps-bar');
    const memoryValue = document.getElementById('memory-value');
    const memoryBar = document.getElementById('memory-bar');
    const gpuValue = document.getElementById('gpu-value');
    const gpuBar = document.getElementById('gpu-bar');

    if (fpsValue && fpsBar) {
        const fps = Math.floor(Math.random() * 10) + 55; // 55-65 FPS
        fpsValue.textContent = fps.toString();
        fpsBar.style.width = `${(fps / 60) * 100}%`;
    }

    if (memoryValue && memoryBar) {
        const memory = Math.floor(Math.random() * 20) + 40; // 40-60%
        memoryValue.textContent = `${memory}%`;
        memoryBar.style.width = `${memory}%`;
        memoryBar.className = `metric-fill ${memory > 70 ? 'danger' : memory > 50 ? 'warning' : 'success'}`;
    }

    if (gpuValue && gpuBar) {
        const gpu = Math.floor(Math.random() * 15) + 25; // 25-40%
        gpuValue.textContent = `${gpu}%`;
        gpuBar.style.width = `${gpu}%`;
    }
}

// 键盘事件处理
function handleKeyboardEvents() {
    document.addEventListener('keydown', (event) => {
        switch (event.key.toLowerCase()) {
            case 'm':
                setEditMode('move');
                break;
            case 'r':
                setEditMode('rotate');
                break;
            case 's':
                if (!event.ctrlKey) {
                    setEditMode('select');
                }
                break;
            case 'delete':
                if (selectedPoint) {
                    // 删除选中的点
                    console.log('删除点:', selectedPoint);
                    addOperationHistory('删除点', `删除路径点 ID: ${selectedPoint.id}`);
                }
                break;
        }

        // Ctrl + S 保存
        if (event.ctrlKey && event.key.toLowerCase() === 's') {
            event.preventDefault();
            saveProject();
        }
    });
}

// 保存项目
function saveProject() {
    showNotification('项目保存功能正在开发中...', 'info');
    addOperationHistory('保存项目', '尝试保存当前项目');
}

// 全屏切换
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        document.exitFullscreen();
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', async function() {
    console.log('路径编辑工作台初始化开始...');

    // 初始化3D场景
    initThreeJS();

    // 初始化2D Canvas
    canvas2DManager.init();

    // 初始化视图模式管理器
    viewModeManager.init();

    // 初始化GUI工具栏
    initGUIToolbar();

    // 初始化事件监听
    handleKeyboardEvents();

    // 尝试恢复保存的样本状态
    const restored = await pathManager.restoreFromSavedState();
    if (!restored) {
        console.log('没有保存的样本状态，请点击"样本选择"按钮选择数据');
    }

    // 初始化UI事件
    const sampleButton = document.getElementById('sample-button');
    if (sampleButton) {
        sampleButton.addEventListener('click', showSampleSelectionModal);
    }

    const saveButton = document.getElementById('save-project');
    if (saveButton) {
        saveButton.addEventListener('click', saveProject);
    }

    const fullscreenButton = document.getElementById('fullscreen-toggle');
    if (fullscreenButton) {
        fullscreenButton.addEventListener('click', toggleFullscreen);
    }

    const pathThicknessSlider = document.getElementById('path-thickness-slider');
    if (pathThicknessSlider) {
        pathThicknessSlider.addEventListener('input', (e) => {
            updatePathThickness(e.target.value);
        });
    }

    // 初始化帮助下拉菜单
    const helpDropdown = document.querySelector('[data-dropdown="help"]');
    if (helpDropdown) {
        helpDropdown.addEventListener('click', (e) => {
            e.preventDefault();
            const dropdown = document.getElementById('help-dropdown');
            if (dropdown) {
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            }
        });
    }

    // 初始化帮助菜单项
    document.querySelectorAll('[data-action]').forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();
            const action = e.currentTarget.dataset.action;
            switch (action) {
                case 'guide':
                    showOperationGuide();
                    break;
                case 'shortcuts':
                    showShortcuts();
                    break;
                case 'about':
                    showNotification('路径编辑工作台 v1.0 - 基于ThreeJS的3D路径编辑器', 'info');
                    break;
            }
            // 关闭下拉菜单
            const helpDropdownMenu = document.getElementById('help-dropdown');
            if (helpDropdownMenu) {
                helpDropdownMenu.style.display = 'none';
            }
        });
    });

    // 关闭模态框事件
    document.querySelectorAll('.close-button-enhanced').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const modal = e.target.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
            }
        });
    });

    // 点击模态框外部关闭
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    });

    // 初始化操作历史
    updateOperationHistoryUI();

    // 初始化统计信息
    updateStatistics();

    console.log('路径编辑工作台初始化完成');
    showNotification('路径编辑工作台初始化完成', 'success');
});
