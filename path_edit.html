<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路径编辑工作台 - 世界模型可视化</title>
    <meta name="description" content="专业的路径编辑规划系统，提供直观的3D可视化界面和强大的路径编辑功能">
    <meta name="keywords" content="路径规划,3D可视化,世界模型,路径编辑,ThreeJS">
    <meta name="author" content="World Model Visualization Team">
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='40' fill='%2306b6d4'/><path d='M30 50 L50 30 L70 50 L50 70 Z' fill='white'/></svg>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/path_edit.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="main-nav">
        <div class="nav-container">
            <div class="nav-content">
                <!-- Logo和标题 -->
                <div class="nav-brand">
                    <div class="nav-logo">
                        <i class="fas fa-route"></i>
                    </div>
                    <div class="nav-title">
                        <h1>路径编辑工作台</h1>
                        <p>Path Editing Workspace</p>
                    </div>
                </div>
                <!-- 导航菜单 -->
                <div class="nav-menu">
                    <a href="index.html" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>仪表板
                    </a>
                    <a href="#" class="nav-link active" data-section="path-editing">
                        <i class="fas fa-route"></i>路径编辑
                    </a>
                    <a href="#" class="nav-link" data-section="analysis">
                        <i class="fas fa-chart-line"></i>分析
                    </a>
                    <a href="#" class="nav-link" data-section="settings">
                        <i class="fas fa-cog"></i>设置
                    </a>
                    <div class="nav-dropdown">
                        <a href="#" class="nav-link" data-dropdown="help">
                            <i class="fas fa-question-circle"></i>帮助
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </a>
                        <div class="dropdown-menu" id="help-dropdown">
                            <a href="#" class="dropdown-item" data-action="guide">
                                <i class="fas fa-book-open"></i>操作指南
                            </a>
                            <a href="#" class="dropdown-item" data-action="shortcuts">
                                <i class="fas fa-keyboard"></i>快捷键
                            </a>
                            <a href="#" class="dropdown-item" data-action="about">
                                <i class="fas fa-info-circle"></i>关于系统
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 右侧工具栏 -->
                <div class="nav-tools">
                    <!-- 状态指示器 -->
                    <div class="status-indicator">
                        <div class="status-dot"></div>
                        <span>编辑模式</span>
                    </div>

                    <!-- 保存按钮 -->
                    <button class="nav-tool-btn" id="save-project" title="保存项目">
                        <i class="fas fa-save"></i>
                    </button>

                    <!-- 全屏按钮 -->
                    <button id="fullscreen-toggle" class="nav-tool-btn">
                        <i class="fas fa-expand"></i>
                    </button>

                    <!-- 移动端菜单按钮 -->
                    <button id="mobile-menu-toggle" class="nav-tool-btn mobile-only">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <div class="main-container">
            <!-- 主要内容网格 -->
            <div class="main-grid">
                <!-- 左侧控制面板 -->
                <div class="left-panel">
                    <!-- 样本选择面板 -->
                    <div class="panel-container">
                        <div class="panel-header">
                            <h2 class="panel-title">
                                <i class="fas fa-database"></i>
                                样本选择
                            </h2>
                            <span class="status-badge status-active">活跃</span>
                        </div>

                        <div class="panel-content">
                            <div class="button-group">
                                <button id="sample-button" class="tech-button-primary">
                                    <i class="fas fa-database"></i>
                                    样本选择
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>

                            <!-- 样本统计信息 -->
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-value" id="available-samples">12</div>
                                    <div class="stat-label">可用样本</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-value" id="selected-samples">0</div>
                                    <div class="stat-label">已选择</div>
                                </div>
                            </div>
                        </div>

                        <!-- 路径点容器 -->
                        <div class="points-section">
                            <div class="points-header">
                                <h3 class="points-title">
                                    <i class="fas fa-route"></i>路径点列表
                                </h3>
                                <span class="points-count" id="points-count">0 个点</span>
                            </div>
                            <div id="points-container" class="points-container"></div>
                        </div>
                    </div>
                </div>

                <!-- 主要工作区域 -->
                <div class="main-workspace">
                    <!-- 路径编辑主面板 -->
                    <div class="workspace-panel">
                        <div class="workspace-header">
                            <div class="workspace-title-section">
                                <div class="workspace-icon">
                                    <i class="fas fa-route"></i>
                                </div>
                                <div class="workspace-title-text">
                                    <h2 class="workspace-title">路径编辑工作台</h2>
                                    <p class="workspace-subtitle">Path Planning & Editing Workspace</p>
                                </div>
                          
                                <!-- 工具栏 -->
                                <div class="edit-toolbar">
                                    <!-- 2D/3D切换标签页 -->
                                    <div class="view-mode-tabs">
                                        <button class="view-tab active" data-mode="3d" id="tab-3d">
                                            <i class="fas fa-cube"></i>
                                            <span>3D视图</span>
                                        </button>
                                        <button class="view-tab" data-mode="2d" id="tab-2d">
                                            <i class="fas fa-map"></i>
                                            <span>2D视图</span>
                                        </button>
                                    </div>

                                    <div class="toolbar-separator"></div>

                                    <!-- 模式指示器 -->
                                    <div class="mode-indicator-container">
                                        <div class="mode-indicator" id="mode-indicator">
                                            <div class="mode-icon">
                                                <i class="fas fa-arrows-alt"></i>
                                            </div>
                                            <div class="mode-info">
                                                <span class="mode-name">移动模式</span>
                                                <span class="mode-shortcut">按 M 切换</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="toolbar-separator"></div>

                                    <!-- 编辑工具 -->
                                    <div class="toolbar-group">
                                        <button class="toolbar-btn active" data-tool="select" title="选择工具">
                                            <i class="fas fa-mouse-pointer"></i>
                                        </button>
                                        <button class="toolbar-btn" data-tool="move" title="移动模式" onclick="setEditMode('move')">
                                            <i class="fas fa-arrows-alt"></i>
                                        </button>
                                        <button class="toolbar-btn" data-tool="rotate" title="旋转模式" onclick="setEditMode('rotate')">
                                            <i class="fas fa-sync-alt"></i>
                                        </button>
                                        <button class="toolbar-btn" data-tool="delete" title="删除工具">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>

                                    <div class="toolbar-separator"></div>

                                    <!-- 路径样式工具 -->
                                    <div class="toolbar-group">
                                        <div class="path-thickness-control">
                                            <label class="thickness-label">
                                                <i class="fas fa-minus"></i>
                                                <input type="range" id="path-thickness-slider" min="1" max="10" value="3" step="0.5" class="thickness-slider">
                                                <i class="fas fa-plus"></i>
                                            </label>
                                            <span class="thickness-value" id="thickness-value">3.0</span>
                                        </div>
                                    </div>

                                    <div class="toolbar-separator"></div>

                                    <!-- 视图工具 -->
                                    <div class="toolbar-group">
                                        <button class="toolbar-btn" data-tool="zoom-in" title="放大">
                                            <i class="fas fa-search-plus"></i>
                                        </button>
                                        <button class="toolbar-btn" data-tool="zoom-out" title="缩小">
                                            <i class="fas fa-search-minus"></i>
                                        </button>
                                        <button class="toolbar-btn" data-tool="fit" title="适应窗口">
                                            <i class="fas fa-expand-arrows-alt"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- 全屏按钮 -->
                                <button class="panel-action-btn fullscreen-btn" data-target="processed-video-container" title="全屏">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 状态栏 -->
                        <div class="workspace-status">
                            <div class="status-left">
                                <span class="status-item">
                                    <i class="fas fa-mouse-pointer"></i>
                                    <span id="current-mode">移动模式</span>
                                </span>
                                <span class="status-item">
                                    <i class="fas fa-crosshairs"></i>
                                    <span id="cursor-coords">坐标: (0, 0)</span>
                                </span>
                                <span class="status-item">
                                    <i class="fas fa-search"></i>
                                    <span id="zoom-level">缩放: 100%</span>
                                </span>
                            </div>
                            <div class="status-right">
                                <div class="status-item layer-selector">
                                    <i class="fas fa-layer-group"></i>
                                    <span>图层:</span>
                                    <select id="layer-select" class="layer-select-dropdown">
                                        <option value="path-1" data-color="#06b6d4">主路径</option>
                                        <option value="path-2" data-color="#3b82f6">备用路径</option>
                                        <option value="path-3" data-color="#10b981">优化路径</option>
                                        <option value="all" data-color="#ffffff">显示全部</option>
                                    </select>
                                    <div class="layer-color-indicator" id="layer-color"></div>
                                </div>
                                <span class="status-item">
                                    <i class="fas fa-save"></i>
                                    已保存
                                </span>
                            </div>
                        </div>

                        <!-- 工作区域 -->
                        <div class="workspace-canvas-container">
                            <!-- 3D场景容器 -->
                            <div id="processed-video-container" class="workspace-canvas scene-3d active">
                                <!-- GUI工具栏 -->
                                <div class="gui-toolbar" id="gui-toolbar">
                                    <!-- 点编辑工具组 -->
                                    <div class="gui-group">
                                        <div class="gui-group-title">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span>点编辑</span>
                                        </div>
                                        <div class="gui-controls">
                                            <button class="gui-btn" id="add-point-btn" title="添加路径点">
                                                <i class="fas fa-plus"></i>
                                                <span>添加点</span>
                                            </button>
                                            <button class="gui-btn" id="delete-point-btn" title="删除选中点" disabled>
                                                <i class="fas fa-trash"></i>
                                                <span>删除点</span>
                                            </button>
                                            <button class="gui-btn" id="edit-point-btn" title="编辑选中点" disabled>
                                                <i class="fas fa-edit"></i>
                                                <span>编辑点</span>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- 路径工具组 -->
                                    <div class="gui-group">
                                        <div class="gui-group-title">
                                            <i class="fas fa-route"></i>
                                            <span>路径工具</span>
                                        </div>
                                        <div class="gui-controls">
                                            <button class="gui-btn" id="connect-points-btn" title="连接选中点">
                                                <i class="fas fa-link"></i>
                                                <span>连接</span>
                                            </button>
                                            <button class="gui-btn" id="split-path-btn" title="分割路径">
                                                <i class="fas fa-cut"></i>
                                                <span>分割</span>
                                            </button>
                                            <button class="gui-btn" id="smooth-path-btn" title="平滑路径">
                                                <i class="fas fa-wave-square"></i>
                                                <span>平滑</span>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- 视图控制组 -->
                                    <div class="gui-group">
                                        <div class="gui-group-title">
                                            <i class="fas fa-eye"></i>
                                            <span>视图控制</span>
                                        </div>
                                        <div class="gui-controls">
                                            <button class="gui-btn" id="reset-view-btn" title="重置视图">
                                                <i class="fas fa-home"></i>
                                                <span>重置</span>
                                            </button>
                                            <button class="gui-btn" id="top-view-btn" title="俯视图">
                                                <i class="fas fa-arrow-down"></i>
                                                <span>俯视</span>
                                            </button>
                                            <button class="gui-btn" id="side-view-btn" title="侧视图">
                                                <i class="fas fa-arrow-right"></i>
                                                <span>侧视</span>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- 渲染设置组 -->
                                    <div class="gui-group">
                                        <div class="gui-group-title">
                                            <i class="fas fa-cog"></i>
                                            <span>渲染设置</span>
                                        </div>
                                        <div class="gui-controls">
                                            <div class="gui-control-item">
                                                <label class="gui-label">点大小</label>
                                                <input type="range" id="point-size-slider" min="0.05" max="0.3" step="0.01" value="0.12" class="gui-slider">
                                                <span class="gui-value" id="point-size-value">0.12</span>
                                            </div>
                                            <div class="gui-control-item">
                                                <label class="gui-label">线条粗细</label>
                                                <input type="range" id="line-thickness-slider" min="0.5" max="5" step="0.1" value="3" class="gui-slider">
                                                <span class="gui-value" id="line-thickness-value">3.0</span>
                                            </div>
                                            <div class="gui-control-item">
                                                <label class="gui-label">透明度</label>
                                                <input type="range" id="opacity-slider" min="0.1" max="1" step="0.1" value="0.8" class="gui-slider">
                                                <span class="gui-value" id="opacity-value">0.8</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 工具栏折叠按钮 -->
                                    <button class="gui-toggle-btn" id="gui-toggle-btn" title="折叠/展开工具栏">
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                </div>

                                <!-- 3D场景占位符 -->
                                <div class="scene-placeholder">
                                    <div class="scene-content">
                                        <div class="scene-icon">
                                            <i class="fas fa-cube"></i>
                                        </div>
                                        <h3>3D路径编辑器</h3>
                                        <p>ThreeJS 3D场景将在此处渲染</p>
                                        <div class="scene-features">
                                            <div class="feature-item">
                                                <i class="fas fa-mouse-pointer"></i>
                                                <span>交互式编辑</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-route"></i>
                                                <span>路径规划</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-eye"></i>
                                                <span>实时预览</span>
                                            </div>
                                        </div>
                                        <div class="loading-indicator">
                                            <div class="loading-spinner"></div>
                                            <span>正在初始化3D引擎...</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 状态信息面板 -->
                                <div class="scene-status-panel" id="scene-status-panel">
                                    <div class="status-item">
                                        <span class="status-label">选中点:</span>
                                        <span class="status-value" id="selected-point-info">无</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">坐标:</span>
                                        <span class="status-value" id="point-coordinates">-</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">角度:</span>
                                        <span class="status-value" id="point-angle">-</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">模式:</span>
                                        <span class="status-value" id="edit-mode-status">移动</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 2D场景容器 -->
                            <div id="canvas-2d-container" class="workspace-canvas scene-2d">
                                <canvas id="canvas-2d" class="canvas-2d"></canvas>

                                <!-- 2D场景控制面板 -->
                                <div class="canvas-2d-controls">
                                    <div class="control-group">
                                        <label>缩放:</label>
                                        <button class="control-btn" id="zoom-in-2d" title="放大">
                                            <i class="fas fa-search-plus"></i>
                                        </button>
                                        <span id="zoom-level-2d">100%</span>
                                        <button class="control-btn" id="zoom-out-2d" title="缩小">
                                            <i class="fas fa-search-minus"></i>
                                        </button>
                                        <button class="control-btn" id="reset-zoom-2d" title="重置缩放">
                                            <i class="fas fa-expand-arrows-alt"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- 2D场景状态信息 -->
                                <div class="canvas-2d-status" id="canvas-2d-status">
                                    <div class="status-item">
                                        <span class="status-label">鼠标位置:</span>
                                        <span class="status-value" id="mouse-position-2d">(0, 0)</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">选中点:</span>
                                        <span class="status-value" id="selected-point-2d">无</span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">缩放:</span>
                                        <span class="status-value" id="zoom-display-2d">100%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </main>

    <!-- Sample selection modal -->
    <div id="sample-modal" class="modal">
        <div class="modal-content-enhanced">
            <div class="modal-header">
                <div class="flex items-center space-x-3">
                    <div class="modal-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div>
                        <h2 class="modal-title">样本数据选择</h2>
                        <p class="modal-subtitle">请选择用于路径规划的样本数据</p>
                    </div>
                </div>
                <button class="close-button-enhanced">&times;</button>
            </div>

            <div class="modal-body">
                <div class="sample-grid" id="sample-grid">
                    <!-- 样本卡片将通过JavaScript动态生成 -->
                    <div class="loading-samples">
                        <div class="loading-spinner">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                        <p>正在加载样本数据...</p>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeSampleModal()">
                    <i class="fas fa-times mr-2"></i>取消
                </button>
                <button class="btn-primary" id="confirm-sample">
                    <i class="fas fa-check mr-2"></i>确认选择
                </button>
            </div>
        </div>
    </div>

    <!-- Point edit modal -->
    <div id="point-edit-modal" class="modal">
        <div class="modal-content-enhanced">
            <div class="modal-header">
                <div class="flex items-center space-x-3">
                    <div class="modal-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div>
                        <h2 class="modal-title">编辑路径点</h2>
                        <p class="modal-subtitle">调整路径点的位置和方向参数</p>
                    </div>
                </div>
                <button class="close-button-enhanced">&times;</button>
            </div>

            <div class="modal-body">
                <form id="point-edit-form" class="space-y-6">
                    <!-- 坐标设置 -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-crosshairs mr-2"></i>坐标设置
                        </h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group-enhanced">
                                <label for="point-x" class="form-label">
                                    <i class="fas fa-arrows-alt-h mr-2"></i>X坐标
                                </label>
                                <input type="number" id="point-x" name="x" step="0.1" class="form-input-enhanced" placeholder="0.0">
                                <div class="form-help">水平位置坐标</div>
                            </div>
                            <div class="form-group-enhanced">
                                <label for="point-y" class="form-label">
                                    <i class="fas fa-arrows-alt-v mr-2"></i>Y坐标
                                </label>
                                <input type="number" id="point-y" name="y" step="0.1" class="form-input-enhanced" placeholder="0.0">
                                <div class="form-help">垂直位置坐标</div>
                            </div>
                            <div class="form-group-enhanced">
                                <label for="point-z" class="form-label">
                                    <i class="fas fa-arrows-alt mr-2"></i>Z坐标
                                </label>
                                <input type="number" id="point-z" name="z" step="0.1" class="form-input-enhanced" placeholder="0.0">
                                <div class="form-help">高度坐标</div>
                            </div>
                        </div>
                    </div>

                    <!-- 角度设置 -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-sync mr-2"></i>方向角度
                        </h3>
                        <div class="form-group-enhanced">
                            <label for="point-angle" class="form-label">
                                角度 (°)
                            </label>
                            <div class="angle-input-group">
                                <input type="number" id="point-angle" name="angle" min="0" max="360" step="1" class="form-input-enhanced angle-input" placeholder="0">
                                <div class="angle-slider-wrapper">
                                    <input type="range" id="angle-slider" min="0" max="360" class="angle-slider-enhanced">
                                    <div class="angle-marks">
                                        <span>0°</span>
                                        <span>90°</span>
                                        <span>180°</span>
                                        <span>270°</span>
                                        <span>360°</span>
                                    </div>
                                </div>
                                <div class="angle-preview">
                                    <div class="angle-indicator">
                                        <div class="angle-arrow"></div>
                                    </div>
                                    <div class="angle-value">0°</div>
                                </div>
                            </div>
                            <div class="form-help">设置路径点的朝向角度</div>
                        </div>
                    </div>

                    <!-- 高级设置 -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-cog mr-2"></i>高级设置
                        </h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group-enhanced">
                                <label class="form-label">点类型</label>
                                <select id="point-type" class="form-input-enhanced">
                                    <option value="normal">普通点</option>
                                    <option value="waypoint">路径点</option>
                                    <option value="checkpoint">检查点</option>
                                </select>
                            </div>
                            <div class="form-group-enhanced">
                                <label class="form-label">编辑状态</label>
                                <select id="point-flag" class="form-input-enhanced">
                                    <option value="0">不可编辑</option>
                                    <option value="1">可编辑</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closePointEditModal()">
                    <i class="fas fa-times mr-2"></i>取消
                </button>
                <button type="button" class="btn-danger" id="delete-point">
                    <i class="fas fa-trash mr-2"></i>删除
                </button>
                <button type="submit" form="point-edit-form" class="btn-primary">
                    <i class="fas fa-save mr-2"></i>保存更改
                </button>
            </div>
        </div>
    </div>

    <!-- 2D点编辑模态框 -->
    <div id="point-edit-2d-modal" class="modal">
        <div class="modal-content-enhanced">
            <div class="modal-header">
                <div class="flex items-center space-x-3">
                    <div class="modal-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div>
                        <h2 class="modal-title">编辑2D路径点</h2>
                        <p class="modal-subtitle">调整路径点的2D坐标和角度参数</p>
                    </div>
                </div>
                <button class="close-button-enhanced" onclick="closePoint2DEditModal()">&times;</button>
            </div>

            <div class="modal-body">
                <form id="point-edit-2d-form" class="space-y-6">
                    <!-- 2D坐标设置 -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-crosshairs mr-2"></i>2D坐标设置
                        </h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group-enhanced">
                                <label for="point-2d-x" class="form-label">
                                    <i class="fas fa-arrows-alt-h mr-2"></i>X坐标
                                </label>
                                <input type="number" id="point-2d-x" name="x" step="0.1" class="form-input-enhanced" placeholder="0.0">
                                <div class="form-help">水平位置坐标</div>
                            </div>
                            <div class="form-group-enhanced">
                                <label for="point-2d-y" class="form-label">
                                    <i class="fas fa-arrows-alt-v mr-2"></i>Y坐标
                                </label>
                                <input type="number" id="point-2d-y" name="y" step="0.1" class="form-input-enhanced" placeholder="0.0">
                                <div class="form-help">垂直位置坐标</div>
                            </div>
                        </div>
                    </div>

                    <!-- 角度设置 -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-sync mr-2"></i>方向角度
                        </h3>
                        <div class="form-group-enhanced">
                            <label for="point-2d-angle" class="form-label">
                                角度 (°)
                            </label>
                            <div class="angle-input-group">
                                <input type="number" id="point-2d-angle" name="angle" min="0" max="360" step="1" class="form-input-enhanced angle-input" placeholder="0">
                                <div class="angle-slider-wrapper">
                                    <input type="range" id="angle-2d-slider" min="0" max="360" class="angle-slider-enhanced">
                                    <div class="angle-marks">
                                        <span>0°</span>
                                        <span>90°</span>
                                        <span>180°</span>
                                        <span>270°</span>
                                        <span>360°</span>
                                    </div>
                                </div>
                                <div class="angle-preview">
                                    <div class="angle-indicator">
                                        <div class="angle-arrow" id="angle-arrow-2d"></div>
                                    </div>
                                    <div class="angle-value" id="angle-value-2d">0°</div>
                                </div>
                            </div>
                            <div class="form-help">设置路径点的朝向角度</div>
                        </div>
                    </div>

                    <!-- 点信息显示 -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-info-circle mr-2"></i>点信息
                        </h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group-enhanced">
                                <label class="form-label">点ID</label>
                                <input type="text" id="point-2d-id" class="form-input-enhanced" readonly>
                            </div>
                            <div class="form-group-enhanced">
                                <label class="form-label">编辑状态</label>
                                <select id="point-2d-flag" class="form-input-enhanced">
                                    <option value="0">不可编辑</option>
                                    <option value="1">可编辑</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closePoint2DEditModal()">
                    <i class="fas fa-times mr-2"></i>取消
                </button>
                <button type="button" class="btn-danger" id="delete-point-2d">
                    <i class="fas fa-trash mr-2"></i>删除
                </button>
                <button type="submit" form="point-edit-2d-form" class="btn-primary">
                    <i class="fas fa-save mr-2"></i>保存更改
                </button>
            </div>
        </div>
    </div>

    <!-- 角度调整控制器 -->
    <div id="angle-control" class="angle-control">
        <div class="angle-control-line"></div>
        <div class="angle-control-handle"></div>
    </div>

    <!-- 操作指南模态框 -->
    <div id="operation-guide-modal" class="modal">
        <div class="modal-content-enhanced">
            <div class="modal-header">
                <div class="modal-icon">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div>
                    <h2 class="modal-title">操作指南</h2>
                    <p class="modal-subtitle">路径编辑器使用说明</p>
                </div>
                <button class="close-button-enhanced" onclick="closeOperationGuide()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="guide-sections">
                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-mouse"></i>鼠标操作
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <kbd>左键单击</kbd>
                                <span>选择路径点</span>
                            </div>
                            <div class="guide-item">
                                <kbd>左键拖拽</kbd>
                                <span>移动选中的路径点</span>
                            </div>
                            <div class="guide-item">
                                <kbd>右键单击</kbd>
                                <span>旋转路径点方向</span>
                            </div>
                            <div class="guide-item">
                                <kbd>双击</kbd>
                                <span>编辑路径点属性</span>
                            </div>
                            <div class="guide-item">
                                <kbd>滚轮</kbd>
                                <span>缩放3D视图</span>
                            </div>
                        </div>
                    </div>

                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-keyboard"></i>键盘快捷键
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <kbd>M</kbd>
                                <span>切换移动/旋转模式</span>
                            </div>
                            <div class="guide-item">
                                <kbd>A</kbd>
                                <span>添加新路径点</span>
                            </div>
                            <div class="guide-item">
                                <kbd>Delete</kbd>
                                <span>删除选中的路径点</span>
                            </div>
                            <div class="guide-item">
                                <kbd>Ctrl + S</kbd>
                                <span>保存当前项目</span>
                            </div>
                            <div class="guide-item">
                                <kbd>Ctrl + Z</kbd>
                                <span>撤销上一步操作</span>
                            </div>
                            <div class="guide-item">
                                <kbd>Ctrl + Y</kbd>
                                <span>重做操作</span>
                            </div>
                        </div>
                    </div>

                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-route"></i>路径管理
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <span class="guide-color" style="background: #06b6d4;"></span>
                                <span>主路径 - 默认规划路径</span>
                            </div>
                            <div class="guide-item">
                                <span class="guide-color" style="background: #3b82f6;"></span>
                                <span>备用路径 - 备选方案</span>
                            </div>
                            <div class="guide-item">
                                <span class="guide-color" style="background: #10b981;"></span>
                                <span>优化路径 - 优化后的路径</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeOperationGuide()">
                    <i class="fas fa-times"></i>关闭
                </button>
                <button type="button" class="btn-primary" onclick="showShortcuts()">
                    <i class="fas fa-bolt"></i>查看快捷键
                </button>
            </div>
        </div>
    </div>

    <!-- Success notification -->
    <div id="success-notification" class="notification">
        <div class="notification-content">
            <p><i class="fas fa-check-circle"></i> 操作成功完成！</p>
        </div>
    </div>

    <!-- Error notification -->
    <div id="error-notification" class="notification error">
        <div class="notification-content">
            <p><i class="fas fa-exclamation-circle"></i> 操作失败！</p>
        </div>
    </div>

    <!-- Info notification -->
    <div id="info-notification" class="notification info">
        <div class="notification-content">
            <p><i class="fas fa-info-circle"></i> 信息提示</p>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <!-- Three.js OrbitControls -->
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <script src="path_edit.js"></script>
</body>
</html>
