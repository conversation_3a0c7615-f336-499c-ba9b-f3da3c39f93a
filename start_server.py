#!/usr/bin/env python3
"""
简单的HTTP服务器，用于本地开发测试
解决CORS跨域问题
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse, parse_qs
import json

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """支持CORS的HTTP请求处理器"""
    
    def end_headers(self):
        # 添加CORS头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        """处理预检请求"""
        self.send_response(200)
        self.end_headers()
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        
        # 如果是API请求，转发到真实API或返回模拟数据
        if parsed_path.path.startswith('/api/'):
            self.handle_api_request(parsed_path)
        else:
            # 处理静态文件
            super().do_GET()
    
    def handle_api_request(self, parsed_path):
        """处理API请求"""
        try:
            if '/api/index/sample' in parsed_path.path and 'server=1' in parsed_path.query:
                # 样本列表接口
                self.send_mock_samples()
            elif '/api/index/simples' in parsed_path.path:
                # 样本数据接口
                query_params = parse_qs(parsed_path.query)
                sample_id = query_params.get('id', [None])[0]
                if sample_id:
                    self.send_mock_sample_data(sample_id)
                else:
                    self.send_error_response('缺少样本ID参数')
            else:
                self.send_error_response('未知的API端点')
        except Exception as e:
            self.send_error_response(f'服务器错误: {str(e)}')
    
    def send_mock_samples(self):
        """发送模拟样本列表"""
        mock_data = {
            "code": 1,
            "msg": "success",
            "time": 1749697351,
            "data": {
                "code": 1,
                "msg": "success",
                "data": [
                    {
                        "id": 1,
                        "name": "上海某座大桥",
                        "image": "/storage/default/20250612/bridge.png",
                        "remark": "测试样本 - 桥梁场景",
                        "create_time": 1747103250,
                        "update_time": 1749697261
                    },
                    {
                        "id": 2,
                        "name": "威尼斯城",
                        "image": "/storage/default/20250612/venice.png",
                        "remark": "测试样本 - 城市场景",
                        "create_time": 1749695587,
                        "update_time": 1749697276
                    },
                    {
                        "id": 3,
                        "name": "某处桥梁入口",
                        "image": "/storage/default/20250612/entrance.png",
                        "remark": "测试样本 - 入口场景",
                        "create_time": 1749695629,
                        "update_time": 1749697299
                    }
                ]
            }
        }
        self.send_json_response(mock_data)
    
    def send_mock_sample_data(self, sample_id):
        """发送模拟样本数据"""
        mock_data_map = {
            "1": [
                {
                    "path_points": [
                        {"angle": -120, "flag": 0, "id": 0, "x": -187, "y": -15, "z": 25},
                        {"angle": -120, "flag": 0, "id": 1, "x": -188.5, "y": -17.6, "z": 25},
                        {"angle": -120, "flag": 0, "id": 2, "x": -190, "y": -20.2, "z": 25},
                        {"angle": -120, "flag": 0, "id": 3, "x": -191.5, "y": -22.8, "z": 25},
                        {"angle": -115, "flag": 1, "id": 4, "x": -191.5, "y": -22.8, "z": 28},
                        {"angle": -110, "flag": 1, "id": 5, "x": -192.5, "y": -25.6, "z": 28},
                        {"angle": -110, "flag": 1, "id": 6, "x": -193.6, "y": -28.4, "z": 28},
                        {"angle": -110, "flag": 1, "id": 7, "x": -194.6, "y": -31.3, "z": 31},
                        {"angle": -105, "flag": 1, "id": 8, "x": -195.4, "y": -34.1, "z": 34},
                        {"angle": -100, "flag": 1, "id": 9, "x": -195.9, "y": -37.1, "z": 34}
                    ],
                    "id": 1,
                    "sample_id": 1,
                    "name": "原路径",
                    "status": "opt0",
                    "vadio": "./1.mp4"
                },
                {
                    "path_points": [
                        {"angle": -120, "flag": 0, "id": 0, "x": -187, "y": -15, "z": 25},
                        {"angle": -120, "flag": 0, "id": 1, "x": -188.5, "y": -17.6, "z": 25},
                        {"angle": -120, "flag": 0, "id": 2, "x": -190, "y": -20.2, "z": 25},
                        {"angle": -120, "flag": 0, "id": 3, "x": -191.5, "y": -22.8, "z": 25},
                        {"angle": -125, "flag": 1, "id": 4, "x": -191.5, "y": -22.8, "z": 25},
                        {"angle": -130, "flag": 1, "id": 5, "x": -193.4, "y": -25.1, "z": 25},
                        {"angle": -130, "flag": 1, "id": 6, "x": -193.4, "y": -25.1, "z": 28},
                        {"angle": -135, "flag": 1, "id": 7, "x": -193.4, "y": -25.1, "z": 31},
                        {"angle": -140, "flag": 1, "id": 8, "x": -193.4, "y": -25.1, "z": 34},
                        {"angle": -145, "flag": 1, "id": 9, "x": -193.4, "y": -25.1, "z": 37}
                    ],
                    "id": 2,
                    "sample_id": 1,
                    "name": "预测路径1",
                    "status": "opt1",
                    "vadio": ""
                },
                {
                    "path_points": [
                        {"angle": -120, "flag": 0, "id": 0, "x": -187, "y": -15, "z": 25},
                        {"angle": -120, "flag": 0, "id": 1, "x": -188.5, "y": -17.6, "z": 25},
                        {"angle": -120, "flag": 0, "id": 2, "x": -190, "y": -20.2, "z": 25},
                        {"angle": -120, "flag": 0, "id": 3, "x": -191.5, "y": -22.8, "z": 25},
                        {"angle": -125, "flag": 1, "id": 4, "x": -191.5, "y": -22.8, "z": 25},
                        {"angle": -130, "flag": 1, "id": 5, "x": -193.4, "y": -25.1, "z": 25},
                        {"angle": -130, "flag": 1, "id": 6, "x": -193.4, "y": -25.1, "z": 28},
                        {"angle": -135, "flag": 1, "id": 7, "x": -193.4, "y": -25.1, "z": 31},
                        {"angle": -140, "flag": 1, "id": 8, "x": -193.4, "y": -25.1, "z": 34},
                        {"angle": -145, "flag": 1, "id": 9, "x": -193.4, "y": -25.1, "z": 37}
                    ],
                    "id": 3,
                    "sample_id": 1,
                    "name": "预测路径2",
                    "status": "opt1",
                    "vadio": ""
                }
            ],
            "2": [
                {
                    "path_points": [
                        {"angle": 0, "flag": 0, "id": 0, "x": -3, "y": 0, "z": 0},
                        {"angle": 5, "flag": 1, "id": 1, "x": -1, "y": 0.2, "z": 0},
                        {"angle": 10, "flag": 1, "id": 2, "x": 1, "y": 0.4, "z": 0},
                        {"angle": 15, "flag": 1, "id": 3, "x": 3, "y": 0.6, "z": 0}
                    ],
                    "id": 4,
                    "sample_id": 2,
                    "name": "主车道",
                    "status": "opt0",
                    "vadio": "./1.mp4"
                },
                {
                    "path_points": [
                        {"angle": 0, "flag": 1, "id": 0, "x": -3, "y": -0.5, "z": 0},
                        {"angle": 8, "flag": 1, "id": 1, "x": -1, "y": -0.3, "z": 0},
                        {"angle": 12, "flag": 1, "id": 2, "x": 1, "y": -0.1, "z": 0},
                        {"angle": 18, "flag": 1, "id": 3, "x": 3, "y": 0.1, "z": 0}
                    ],
                    "id": 5,
                    "sample_id": 2,
                    "name": "超车道",
                    "status": "opt1",
                    "vadio": ""
                }
            ],
            "3": [
                {
                    "path_points": [
                        {"angle": 0, "flag": 1, "id": 0, "x": -2, "y": 0, "z": 0},
                        {"angle": 0, "flag": 1, "id": 1, "x": -1, "y": 0, "z": 0},
                        {"angle": 0, "flag": 1, "id": 2, "x": 0, "y": 0, "z": 0},
                        {"angle": 0, "flag": 1, "id": 3, "x": 1, "y": 0, "z": 0},
                        {"angle": 0, "flag": 1, "id": 4, "x": 2, "y": 0, "z": 0}
                    ],
                    "id": 6,
                    "sample_id": 3,
                    "name": "直行路径",
                    "status": "opt0",
                    "vadio": "./1.mp4"
                }
            ]
        }
        
        sample_data = mock_data_map.get(str(sample_id), [])
        response_data = {
            "code": 1,
            "msg": "success",
            "time": 1749700022,
            "data": {
                "code": 1,
                "msg": "success",
                "data": sample_data
            }
        }
        self.send_json_response(response_data)
    
    def send_json_response(self, data):
        """发送JSON响应"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode('utf-8'))
    
    def send_error_response(self, message):
        """发送错误响应"""
        error_data = {
            "code": 0,
            "msg": message,
            "time": 1749700022,
            "data": None
        }
        self.send_response(400)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        json_data = json.dumps(error_data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode('utf-8'))

def main():
    """主函数"""
    port = 8000
    
    # 检查端口是否被占用
    try:
        with socketserver.TCPServer(("", port), CORSHTTPRequestHandler) as httpd:
            print(f"🚀 服务器启动成功!")
            print(f"📍 本地地址: http://localhost:{port}")
            print(f"🌐 网络地址: http://127.0.0.1:{port}")
            print(f"📁 服务目录: {os.getcwd()}")
            print(f"🔧 支持CORS跨域请求")
            print(f"📡 模拟API接口已启用")
            print(f"\n💡 使用说明:")
            print(f"   - 在浏览器中访问 http://localhost:{port}")
            print(f"   - 按 Ctrl+C 停止服务器")
            print(f"\n🔄 服务器运行中...")
            
            httpd.serve_forever()
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {port} 已被占用，请尝试其他端口或关闭占用该端口的程序")
        else:
            print(f"❌ 启动服务器失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print(f"\n🛑 服务器已停止")
        sys.exit(0)

if __name__ == "__main__":
    main()
