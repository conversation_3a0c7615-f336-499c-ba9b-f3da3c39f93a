<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>世界模型可视化展示大屏</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="main-nav">
        <div class="nav-container">
            <div class="nav-content">
                <!-- Logo和标题 -->
                <div class="nav-brand">
                    <div class="nav-logo">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="nav-title">
                        <h1>世界模型可视化展示大屏</h1>
                        <p>World Model Visualization Dashboard</p>
                    </div>
                </div>
                
                <!-- 导航菜单 -->
                <div class="nav-menu">
                    <a href="#" class="nav-link active" data-section="dashboard">
                        <i class="fas fa-tachometer-alt"></i>仪表板
                    </a>
                    <a href="#" class="nav-link" data-section="analysis">
                        <i class="fas fa-chart-line"></i>分析
                    </a>
                    <a href="#" class="nav-link" data-section="settings">
                        <i class="fas fa-cog"></i>设置
                    </a>
                    <a href="#" class="nav-link" data-section="help">
                        <i class="fas fa-question-circle"></i>帮助
                    </a>
                </div>
                
                <!-- 右侧工具栏 -->
                <div class="nav-tools">
                    <!-- 状态指示器 -->
                    <div class="status-indicator">
                        <div class="status-dot"></div>
                        <span>系统正常</span>
                    </div>
                    
                    <!-- 全屏按钮 -->
                    <button id="fullscreen-toggle" class="nav-tool-btn">
                        <i class="fas fa-expand"></i>
                    </button>
                    
                    <!-- 移动端菜单按钮 -->
                    <button id="mobile-menu-toggle" class="nav-tool-btn mobile-only">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 移动端菜单 -->
        <div id="mobile-menu" class="mobile-menu hidden">
            <div class="mobile-menu-content">
                <a href="#" class="mobile-nav-link" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i>仪表板
                </a>
                <a href="#" class="mobile-nav-link" data-section="analysis">
                    <i class="fas fa-chart-line"></i>分析
                </a>
                <a href="#" class="mobile-nav-link" data-section="settings">
                    <i class="fas fa-cog"></i>设置
                </a>
                <a href="#" class="mobile-nav-link" data-section="help">
                    <i class="fas fa-question-circle"></i>帮助
                </a>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main style="padding-top: 120px; padding-bottom: 24px; padding-left: 24px; padding-right: 24px; min-height: 100vh;">
        <div style="max-width: 1400px; margin: 0 auto;">
            <!-- 快捷操作工具栏 -->
            <div class="toolbar-container">
                <div class="toolbar-content">
                    <div class="toolbar-left">
                        <h2 class="toolbar-title">
                            <i class="fas fa-tools"></i>快捷操作
                        </h2>
                        <div class="toolbar-buttons">
                            <button class="quick-action-btn" data-action="save" title="保存项目">
                                <i class="fas fa-save"></i>
                            </button>
                            <button class="quick-action-btn" data-action="load" title="加载项目">
                                <i class="fas fa-folder-open"></i>
                            </button>
                            <button class="quick-action-btn" data-action="export" title="导出数据">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="toolbar-right">
                        <!-- 进度指示器 -->
                        <div class="progress-indicator">
                            <span class="progress-label">处理进度:</span>
                            <div class="progress-bar-container">
                                <div class="progress-bar-fill" style="width: 75%"></div>
                            </div>
                            <span class="progress-value">75%</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主要内容网格 -->
            <div class="main-grid">
                <!-- 左侧控制面板 -->
                <div class="left-panel">
                    <!-- 样本选择面板 -->
                    <div class="panel-container">
                        <div class="panel-header">
                            <h2 class="panel-title">
                                <i class="fas fa-database"></i>
                                样本选择
                            </h2>
                            <span class="status-badge status-active">活跃</span>
                        </div>
                        
                        <div class="panel-content">
                            <div class="info-card">
                                <i class="fas fa-info-circle"></i>
                                <span>选择不同的样本数据进行路径规划分析</span>
                            </div>
                            
                            <div class="button-group">
                                <button id="sample-button" class="tech-button-primary">
                                    <i class="fas fa-database"></i>
                                    样本选择
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                                
                                <button id="fetch-points-button" class="tech-button-secondary">
                                    <i class="fas fa-download"></i>
                                    获取路径点
                                    <i class="fas fa-arrow-down"></i>
                                </button>
                            </div>
                            
                            <!-- 样本统计信息 -->
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-value">12</div>
                                    <div class="stat-label">可用样本</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-value">3</div>
                                    <div class="stat-label">已选择</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 路径点容器 -->
                        <div class="points-section">
                            <div class="points-header">
                                <h3 class="points-title">
                                    <i class="fas fa-route"></i>路径点列表
                                </h3>
                                <span class="points-count" id="points-count">0 个点</span>
                            </div>
                            <div id="points-container" class="points-container"></div>
                        </div>
                    </div>

                    <!-- 原视频显示面板 -->
                    <div class="panel-container">
                        <div class="panel-header">
                            <h2 class="panel-title">
                                <i class="fas fa-video"></i>
                                原视频显示
                            </h2>
                            <div class="panel-actions">
                                <span class="status-badge status-playing">播放中</span>
                                <button class="panel-action-btn fullscreen-btn" data-target="original-video-container" title="全屏">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="video-panel">
                            <div class="video-wrapper">
                                <video id="video1" class="video-player" src="./1.mp4" loop muted></video>
                            </div>
                        </div>
                        
                        <div id="original-video-container" class="video-container hidden"></div>
                    </div>
                </div>
                
                <!-- 主要工作区域 -->
                <div class="main-workspace">
                    <!-- 路径编辑主面板 -->
                    <div class="workspace-panel">
                        <div class="workspace-header">
                            <div class="workspace-title-section">
                                <div class="workspace-icon">
                                    <i class="fas fa-route"></i>
                                </div>
                                <div class="workspace-title-text">
                                    <h2 class="workspace-title">路径编辑工作台</h2>
                                    <p class="workspace-subtitle">Path Planning & Editing Workspace</p>
                                </div>
                            </div>
                            
                            <div class="workspace-controls">
                                <!-- 工具栏 -->
                                <div class="edit-toolbar">
                                    <button class="toolbar-btn active" data-tool="select" title="选择工具">
                                        <i class="fas fa-mouse-pointer"></i>
                                    </button>
                                    <button class="toolbar-btn" data-tool="move" title="移动工具">
                                        <i class="fas fa-arrows-alt"></i>
                                    </button>
                                    <button class="toolbar-btn" data-tool="rotate" title="旋转工具">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                    <button class="toolbar-btn" data-tool="add" title="添加点">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                                
                                <!-- 视图控制 -->
                                <div class="view-controls">
                                    <button class="view-control-btn" data-view="2d" title="2D视图">
                                        <i class="fas fa-square"></i>
                                    </button>
                                    <button class="view-control-btn active" data-view="3d" title="3D视图">
                                        <i class="fas fa-cube"></i>
                                    </button>
                                </div>
                                
                                <!-- 全屏按钮 -->
                                <button class="panel-action-btn fullscreen-btn" data-target="processed-video-container" title="全屏">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 状态栏 -->
                        <div class="workspace-status">
                            <div class="status-left">
                                <span class="status-item">
                                    <i class="fas fa-mouse-pointer"></i>
                                    选择模式
                                </span>
                                <span class="status-item">
                                    <i class="fas fa-crosshairs"></i>
                                    坐标: (0, 0)
                                </span>
                            </div>
                            <div class="status-right">
                                <span class="status-item">
                                    <i class="fas fa-save"></i>
                                    已保存
                                </span>
                            </div>
                        </div>
                        
                        <!-- 3D工作区域 -->
                        <div class="workspace-canvas-container">
                            <div id="processed-video-container" class="workspace-canvas"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 模态框等其他内容保持原样 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
