<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .loading {
            color: #007bff;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
    </style>
</head>
<body>
    <h1>API接口测试</h1>
    
    <div class="test-section">
        <h2>1. 测试样本列表接口</h2>
        <button class="test-button" onclick="testSamplesList()">获取样本列表</button>
        <div id="samples-result" class="result">点击按钮测试接口...</div>
    </div>
    
    <div class="test-section">
        <h2>2. 测试样本数据接口</h2>
        <input type="number" id="sample-id" placeholder="输入样本ID" value="1" style="padding: 8px; margin: 5px;">
        <button class="test-button" onclick="testSampleData()">获取样本数据</button>
        <div id="sample-data-result" class="result">点击按钮测试接口...</div>
    </div>
    
    <div class="test-section">
        <h2>3. 测试数据转换</h2>
        <button class="test-button" onclick="testDataConversion()">测试数据格式转换</button>
        <div id="conversion-result" class="result">点击按钮测试数据转换...</div>
    </div>

    <script>
        // API配置
        const API_CONFIG = {
            baseUrl: 'https://word.sszhai.com/api/index',
            endpoints: {
                samples: '/sample?server=1',
                sampleData: '/simples?server=1&id='
            }
        };

        // 测试样本列表接口
        async function testSamplesList() {
            const resultDiv = document.getElementById('samples-result');
            resultDiv.innerHTML = '<span class="loading">正在获取样本列表...</span>';
            
            try {
                const response = await fetch(`${API_CONFIG.baseUrl}${API_CONFIG.endpoints.samples}`);
                const result = await response.json();
                
                resultDiv.innerHTML = `<span class="success">✓ 接口调用成功</span>\n\n${JSON.stringify(result, null, 2)}`;
                
                if (result.code === 1 && result.data && result.data.code === 1) {
                    console.log('样本列表:', result.data.data);
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">✗ 接口调用失败</span>\n\n错误信息: ${error.message}`;
                console.error('获取样本列表失败:', error);
            }
        }

        // 测试样本数据接口
        async function testSampleData() {
            const sampleId = document.getElementById('sample-id').value;
            const resultDiv = document.getElementById('sample-data-result');
            
            if (!sampleId) {
                resultDiv.innerHTML = '<span class="error">请输入样本ID</span>';
                return;
            }
            
            resultDiv.innerHTML = '<span class="loading">正在获取样本数据...</span>';
            
            try {
                const response = await fetch(`${API_CONFIG.baseUrl}${API_CONFIG.endpoints.sampleData}${sampleId}`);
                const result = await response.json();
                
                resultDiv.innerHTML = `<span class="success">✓ 接口调用成功</span>\n\n${JSON.stringify(result, null, 2)}`;
                
                if (result.code === 1 && result.data && result.data.code === 1) {
                    console.log('样本数据:', result.data.data);
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">✗ 接口调用失败</span>\n\n错误信息: ${error.message}`;
                console.error('获取样本数据失败:', error);
            }
        }

        // 测试数据转换
        async function testDataConversion() {
            const resultDiv = document.getElementById('conversion-result');
            resultDiv.innerHTML = '<span class="loading">正在测试数据转换...</span>';
            
            try {
                // 模拟API返回的数据
                const mockApiData = [
                    {
                        "path_points": [
                            {"angle": -120, "flag": 0, "id": 0, "x": -187, "y": -15, "z": 25},
                            {"angle": -120, "flag": 0, "id": 1, "x": -188.5, "y": -17.6, "z": 25},
                            {"angle": -115, "flag": 1, "id": 4, "x": -191.5, "y": -22.8, "z": 28}
                        ],
                        "id": 1,
                        "sample_id": 1,
                        "name": "0",
                        "status": "opt0",
                        "vadio": "/storage/default/test.mp4"
                    },
                    {
                        "path_points": [
                            {"angle": -120, "flag": 0, "id": 0, "x": -187, "y": -15, "z": 25},
                            {"angle": -125, "flag": 1, "id": 4, "x": -191.5, "y": -22.8, "z": 25}
                        ],
                        "id": 2,
                        "sample_id": 1,
                        "name": "1",
                        "status": "opt1",
                        "vadio": ""
                    }
                ];

                // 模拟样本信息
                const mockSampleInfo = {
                    id: 1,
                    name: "测试样本",
                    remark: "这是一个测试样本"
                };

                // 转换数据格式
                const convertedData = convertApiDataToInternalFormat(mockApiData, 1, mockSampleInfo);
                
                resultDiv.innerHTML = `<span class="success">✓ 数据转换成功</span>\n\n转换后的数据:\n${JSON.stringify(convertedData, null, 2)}`;
                
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">✗ 数据转换失败</span>\n\n错误信息: ${error.message}`;
                console.error('数据转换失败:', error);
            }
        }

        // 数据转换函数（从主项目复制）
        function convertApiDataToInternalFormat(apiData, sampleId, sampleInfo) {
            if (!apiData || !Array.isArray(apiData)) {
                return null;
            }
            
            const pathColors = ['#06b6d4', '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899', '#14b8a6'];
            
            const convertedData = {
                id: sampleId,
                name: sampleInfo ? sampleInfo.name : `样本 ${sampleId}`,
                description: sampleInfo ? sampleInfo.remark || '路径规划样本' : '路径规划样本',
                type: 'api_sample',
                difficulty: 'medium',
                totalPaths: apiData.length,
                estimatedTime: `${(apiData.length * 0.8).toFixed(1)}分钟`,
                image: sampleInfo ? sampleInfo.image : null,
                paths: []
            };
            
            // 转换路径数据
            apiData.forEach((pathData, index) => {
                const path = {
                    id: `path-${pathData.id}`,
                    name: pathData.name || `路径 ${index + 1}`,
                    color: pathColors[index % pathColors.length],
                    type: pathData.status === 'opt0' ? 'original' : 'predicted',
                    visible: true,
                    status: pathData.status,
                    video: pathData.vadio || '',
                    sample_id: pathData.sample_id,
                    points: []
                };
                
                // 转换路径点数据
                if (pathData.path_points && Array.isArray(pathData.path_points)) {
                    path.points = pathData.path_points.map(point => ({
                        id: point.id,
                        x: point.x,
                        y: point.y,
                        z: point.z,
                        angle: point.angle,
                        flag: point.flag,
                        speed: 30
                    }));
                }
                
                convertedData.paths.push(path);
            });
            
            return convertedData;
        }

        // 页面加载完成后自动测试样本列表
        window.addEventListener('load', () => {
            console.log('页面加载完成，可以开始测试API接口');
        });
    </script>
</body>
</html>
