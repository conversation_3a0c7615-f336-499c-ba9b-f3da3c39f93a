// 重构后的世界模型可视化展示大屏 - 基于参考项目
// Global variables for ThreeJS
let scene, camera, renderer, controls;
let currentSample = null;
let selectedPath = null;
let selectedPoint = null;
let isDragging = false;
let isRotating = false;
let editMode = 'move'; // 'move' or 'rotate'
let raycaster = new THREE.Raycaster();
let mouse = new THREE.Vector2();
let pathThickness = 1; // 全局路径粗细变量
let image_host='https://word.sszhai.com'
let currentViewMode = '3d'; // 当前视图模式：'2d' 或 '3d'
let canvas2D = null;
let ctx2D = null;


// API配置
const API_CONFIG = {
    // 强制使用真实API地址
    baseUrl: 'https://word.sszhai.com/api/index',
    endpoints: {
        samples: '/sample?server=1',
        sampleData: '/simples?server=1&id='
    }
};

// API管理模块
const apiManager = {

    // 获取样本列表
    async fetchSamples() {
        try {
            console.log('正在从API获取样本列表...');
            const response = await fetch(`${API_CONFIG.baseUrl}${API_CONFIG.endpoints.samples}`, {
                method: 'GET',
                mode: 'cors',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('API返回样本列表:', result);

            if (result.code === 1 && result.data && result.data.code === 1) {
                return result.data.data;
            } else {
                throw new Error(result.msg || '获取样本列表失败');
            }
        } catch (error) {
            console.error('获取样本列表失败:', error);
            showNotification('获取样本列表失败: ' + error.message, 'error');
            return [];
        }
    },

    // 获取样本数据
    async fetchSampleData(sampleId) {
        try {
            console.log(`正在从API获取样本${sampleId}的数据...`);
            const response = await fetch(`${API_CONFIG.baseUrl}${API_CONFIG.endpoints.sampleData}${sampleId}`, {
                method: 'GET',
                mode: 'cors',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log(`API返回样本${sampleId}数据:`, result);

            if (result.code === 1 && result.data && result.data.code === 1) {
                return result.data.data;
            } else {
                throw new Error(result.msg || '获取样本数据失败');
            }
        } catch (error) {
            console.error('获取样本数据失败:', error);
            showNotification('获取样本数据失败: ' + error.message, 'error');
            return [];
        }
    },

    // 生成预测视频
    async generatePredictedVideo(pathId) {
        try {
            // 这里应该调用生成视频的API
            // 暂时模拟3-10秒的生成时间
            const generateTime = Math.random() * 7000 + 3000; // 3-10秒

            return new Promise((resolve) => {
                setTimeout(() => {
                    resolve({
                        success: true,
                        videoUrl: '/storage/default/predicted_video.mp4', // 模拟生成的视频URL
                        message: '视频生成成功'
                    });
                }, generateTime);
            });
        } catch (error) {
            console.error('生成预测视频失败:', error);
            throw error;
        }
    },


};

// 状态管理模块
const stateManager = {
    // 保存当前样本状态到localStorage
    saveCurrentSample(sampleId, sampleData) {
        try {
            const state = {
                sampleId: sampleId,
                sampleData: sampleData,
                timestamp: Date.now()
            };
            localStorage.setItem('worldModel_currentSample', JSON.stringify(state));
            console.log('样本状态已保存:', sampleId);
        } catch (error) {
            console.error('保存样本状态失败:', error);
        }
    },

    // 从localStorage加载当前样本状态
    loadCurrentSample() {
        try {
            const stateStr = localStorage.getItem('worldModel_currentSample');
            if (stateStr) {
                const state = JSON.parse(stateStr);
                // 检查状态是否过期（24小时）
                if (Date.now() - state.timestamp < 24 * 60 * 60 * 1000) {
                    console.log('加载保存的样本状态:', state.sampleId);
                    return state;
                } else {
                    console.log('样本状态已过期，清除缓存');
                    this.clearCurrentSample();
                }
            }
        } catch (error) {
            console.error('加载样本状态失败:', error);
        }
        return null;
    },

    // 清除当前样本状态
    clearCurrentSample() {
        try {
            localStorage.removeItem('worldModel_currentSample');
            console.log('样本状态已清除');
        } catch (error) {
            console.error('清除样本状态失败:', error);
        }
    },

    // 保存视图模式状态
    saveViewMode(mode) {
        try {
            localStorage.setItem('worldModel_viewMode', mode);
        } catch (error) {
            console.error('保存视图模式失败:', error);
        }
    },

    // 加载视图模式状态
    loadViewMode() {
        try {
            return localStorage.getItem('worldModel_viewMode') || '3d';
        } catch (error) {
            console.error('加载视图模式失败:', error);
            return '3d';
        }
    }
};

// 数据管理模块
const dataManager = {
    // 缓存的样本列表
    cachedSamples: [],
    // 缓存的样本数据
    cachedSampleData: new Map(),
    // 路径颜色配置
    pathColors: ['#06b6d4', '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899', '#14b8a6'],

    // 获取样本列表
    async getSamples() {
        if (this.cachedSamples.length === 0) {
            this.cachedSamples = await apiManager.fetchSamples();
        }
        return this.cachedSamples;
    },

    // 获取样本数据并转换格式
    async getSampleData(sampleId) {
        if (!this.cachedSampleData.has(sampleId)) {
            const rawData = await apiManager.fetchSampleData(sampleId);
            const convertedData = this.convertApiDataToInternalFormat(rawData, sampleId);
            this.cachedSampleData.set(sampleId, convertedData);
        }
        return this.cachedSampleData.get(sampleId);
    },

    // 将API返回的数据转换为内部格式
    convertApiDataToInternalFormat(apiData, sampleId) {
        console.log('开始转换API数据:', apiData);
        console.log('API数据长度:', apiData ? apiData.length : 0);

        if (!apiData || !Array.isArray(apiData)) {
            console.error('API数据无效:', apiData);
            return null;
        }

        // 找到对应的样本信息
        const sampleInfo = this.cachedSamples.find(s => s.id == sampleId);

        const convertedData = {
            id: sampleId,
            name: sampleInfo ? sampleInfo.name : `样本 ${sampleId}`,
            description: sampleInfo ? sampleInfo.remark || '路径规划样本' : '路径规划样本',
            type: 'api_sample',
            difficulty: 'medium',
            totalPaths: apiData.length,
            estimatedTime: `${(apiData.length * 0.8).toFixed(1)}分钟`,
            image: sampleInfo ? sampleInfo.image : null,
            paths: []
        };

        console.log('准备转换', apiData.length, '条路径数据');

        // 转换路径数据
        apiData.forEach((pathData, index) => {
            console.log(`转换第${index + 1}条路径:`, pathData);
            const path = {
                id: `path-${pathData.id}`,
                name: pathData.name || `路径 ${index + 1}`,
                color: this.pathColors[index % this.pathColors.length],
                type: this.getPathTypeFromStatus(pathData.status),
                visible: true,
                status: pathData.status, // 保存原始状态信息
                video: pathData.vadio || '', // 保存视频URL
                sample_id: pathData.sample_id,
                points: []
            };

            // 转换路径点数据
            if (pathData.path_points && Array.isArray(pathData.path_points)) {
                path.points = pathData.path_points.map(point => ({
                    id: point.id,
                    x: point.x,
                    y: point.y,
                    z: point.z,
                    angle: point.angle,
                    flag: point.flag,
                    speed: 30 // 默认速度，API数据中没有速度信息
                }));
            }

            convertedData.paths.push(path);
        });

        console.log('转换完成，总共', convertedData.paths.length, '条路径');
        console.log('转换后的数据:', convertedData);

        return convertedData;
    },

    // 根据状态获取路径类型
    getPathTypeFromStatus(status) {
        const typeMap = {
            'opt0': 'original',
            'opt1': 'predicted'
        };
        return typeMap[status] || 'unknown';
    },

    // 清除缓存
    clearCache() {
        this.cachedSamples = [];
        this.cachedSampleData.clear();
    }
};

// 路径管理器
const pathManager = {
    currentSample: null,
    currentPath: null,
    selectedPoint: null,
    pathObjects: [], // 存储3D路径对象

    // 设置当前样本 - 支持API数据
    async setCurrentSample(sampleId) {
        try {
            showNotification('正在加载样本数据...', 'info');
            const sampleData = await dataManager.getSampleData(sampleId);

            if (sampleData) {
                this.currentSample = sampleData;
                this.currentPath = null;
                this.selectedPoint = null;
                this.clearScene();

                // 保存状态到localStorage
                stateManager.saveCurrentSample(sampleId, sampleData);

                // 根据样本数据动态定位相机
                this.adjustCameraToSampleData(sampleData);

                this.renderAllPaths();
                this.updateUI();
                showNotification(`样本 "${sampleData.name}" 加载成功`, 'success');
            } else {
                throw new Error('样本数据为空');
            }
        } catch (error) {
            console.error('设置当前样本失败:', error);
            showNotification('加载样本数据失败: ' + error.message, 'error');
        }
    },

    // 从保存的状态恢复样本
    async restoreFromSavedState() {
        const savedState = stateManager.loadCurrentSample();
        if (savedState && savedState.sampleData) {
            try {
                this.currentSample = savedState.sampleData;
                this.currentPath = null;
                this.selectedPoint = null;
                this.clearScene();

                // 根据样本数据动态定位相机
                this.adjustCameraToSampleData(savedState.sampleData);

                this.renderAllPaths();
                this.updateUI();
                showNotification(`已恢复样本 "${savedState.sampleData.name}"`, 'success');
                return true;
            } catch (error) {
                console.error('恢复样本状态失败:', error);
                stateManager.clearCurrentSample();
                return false;
            }
        }
        return false;
    },

    // 获取当前样本
    getCurrentSample() {
        return this.currentSample;
    },

    // 获取当前样本的所有路径
    getCurrentPaths() {
        return this.currentSample ? this.currentSample.paths : [];
    },

    // 获取可见路径
    getVisiblePaths() {
        return this.getCurrentPaths().filter(path => path.visible);
    },
    
    // 切换路径可见性
    togglePathVisibility(pathId) {
        const paths = this.getCurrentPaths();
        const path = paths.find(p => p.id === pathId);
        if (path) {
            path.visible = !path.visible;
            this.renderAllPaths();
            this.updateUI();
        }
    },
    
    // 设置当前编辑路径
    setCurrentPath(pathId) {
        const paths = this.getCurrentPaths();
        const path = paths.find(p => p.id === pathId);
        if (path) {
            this.currentPath = path;
            this.selectedPoint = null;
            this.updateUI();
        }
    },
    
    // 清空3D场景
    clearScene() {
        this.pathObjects.forEach(obj => {
            if (obj.points) {
                obj.points.forEach(pointObj => {
                    scene.remove(pointObj.mesh);
                    if (pointObj.directionIndicator) {
                        scene.remove(pointObj.directionIndicator);
                    }
                });
            }
            if (obj.lines) {
                obj.lines.forEach(line => scene.remove(line));
            }
        });
        this.pathObjects = [];
    },
    
    // 渲染所有路径
    renderAllPaths() {
        if (currentViewMode === '3d') {
            this.clearScene();

            const visiblePaths = this.getVisiblePaths();
            visiblePaths.forEach(path => {
                this.renderPath(path);
            });
        } else if (currentViewMode === '2d') {
            // 渲染2D路径
            render2DPaths();
        }
    },
    
    // 渲染单条路径
    renderPath(path) {
        console.log(`渲染路径: ${path.name} (${path.color}), 包含 ${path.points.length} 个点`);

        const pathObj = {
            id: path.id,
            points: [],
            lines: []
        };

        // 渲染路径点
        path.points.forEach((point) => {
            console.log(`  点 ${point.id}: (${point.x}, ${point.y}, ${point.z}), 角度: ${point.angle}°, 可编辑: ${point.flag === 1 ? '是' : '否'}`);
            const pointObj = this.createPoint(point, path.color);
            pathObj.points.push(pointObj);
        });

        // 渲染连接线
        if (path.points.length > 1) {
            for (let i = 0; i < path.points.length - 1; i++) {
                const line = this.createLine(path.points[i], path.points[i + 1], path.color);
                pathObj.lines.push(line);
                console.log(`  连线: 点${path.points[i].id} -> 点${path.points[i + 1].id} (颜色: ${path.color})`);
            }
        }

        this.pathObjects.push(pathObj);
        console.log(`路径 ${path.name} 渲染完成`);
    },
    
    // 创建3D点
    createPoint(point, pathColor) {
        const geometry = new THREE.SphereGeometry(0.12, 32, 32);
        // 根据flag决定点的颜色：flag=1使用路径颜色，flag=0使用灰色
        const pointColor = point.flag === 1 ? pathColor : '#cccccc';
        const material = new THREE.MeshBasicMaterial({ color: pointColor });
        const mesh = new THREE.Mesh(geometry, material);

        // 使用实际的x,y,z坐标
        mesh.position.set(point.x, point.y, point.z || 0);
        mesh.userData = { point: point, pathColor: pathColor };
        scene.add(mesh);
        // 创建方向指示器
        const directionIndicator = this.createDirectionIndicator(point, pathColor);
        return {
            mesh: mesh,
            directionIndicator: directionIndicator,
            point: point
        };
    },

    // 创建方向指示器
    createDirectionIndicator(point, pathColor) {
        const arrowLength = 0.4;
        const geometry = new THREE.ConeGeometry(0.08, arrowLength, 8);
        // 角度指示器颜色与所在点颜色一致：flag=1使用路径颜色，flag=0使用灰色
        const arrowColor = point.flag === 1 ? pathColor : '#aaaaaa';
        const material = new THREE.MeshBasicMaterial({ color: arrowColor });
        const indicator = new THREE.Mesh(geometry, material);
        // 设置位置和角度
        const angleRad = (point.angle - 90) * (Math.PI / 180);
        indicator.rotation.z = angleRad;
        indicator.position.set(point.x, point.y, point.z || 0);
        indicator.position.x += Math.cos(angleRad) * (arrowLength / 2 + 0.05);
        indicator.position.y += Math.sin(angleRad) * (arrowLength / 2 + 0.05);
        scene.add(indicator);
        return indicator;
    },

    // 创建连接线 - 使用路径颜色和全局粗细
    createLine(point1, point2, pathColor) {
        // 注意：LineBasicMaterial的linewidth在大多数平台上不起作用
        // 我们使用TubeGeometry来创建有实际粗细的线条
        const start = new THREE.Vector3(point1.x, point1.y, point1.z || 0);
        const end = new THREE.Vector3(point2.x, point2.y, point2.z || 0);

        // 创建路径曲线
        const curve = new THREE.LineCurve3(start, end);

        // 使用TubeGeometry创建有粗细的线条
        const tubeGeometry = new THREE.TubeGeometry(curve, 1, pathThickness * 0.1, 8, false);
        const material = new THREE.MeshBasicMaterial({
            color: pathColor,
            transparent: true,
            opacity: 0.8
        });

        const line = new THREE.Mesh(tubeGeometry, material);
        line.userData.isPathLine = true; // 标记为路径线条
        scene.add(line);
        return line;
    },
    
    // 更新UI
    updateUI() {
        this.updatePathsList();
        this.updateLayerSelector();
        this.updateSampleInfo();
    },

    // 更新路径列表
    updatePathsList() {
        const pointsContainer = document.getElementById('points-container');
        const pointsCount = document.getElementById('points-count');

        if (!pointsContainer || !pointsCount) return;

        if (!this.currentSample) {
            pointsContainer.innerHTML = `
                <div class="no-sample-message">
                    <div class="no-sample-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3>未选择样本</h3>
                    <p>请点击"样本选择"按钮选择路径数据</p>
                    <button class="btn-primary-small" onclick="showSampleSelectionModal()">
                        <i class="fas fa-plus"></i>选择样本
                    </button>
                </div>
            `;
            pointsCount.textContent = '0 条路径';
            return;
        }

        const paths = this.getCurrentPaths();
        pointsCount.textContent = `${paths.length} 条路径`;

        pointsContainer.innerHTML = '';

        // 添加样本信息头部
        const sampleHeader = document.createElement('div');
        sampleHeader.className = 'sample-header';
        sampleHeader.innerHTML = `
            <div class="sample-info">
                <div class="sample-title">
                    <i class="fas fa-route"></i>
                    <span>${this.currentSample.name}</span>
                </div>
                <div class="sample-meta">
                    <span class="sample-type">${this.getSampleTypeLabel(this.currentSample.type)}</span>
                    <span class="sample-difficulty difficulty-${this.currentSample.difficulty}">
                        ${this.getDifficultyLabel(this.currentSample.difficulty)}
                    </span>
                </div>
                <p class="sample-description">${this.currentSample.description}</p>
            </div>
            <button class="change-sample-btn" onclick="showSampleSelectionModal()" title="更换样本">
                <i class="fas fa-exchange-alt"></i>
            </button>
        `;
        pointsContainer.appendChild(sampleHeader);

        // 添加路径列表
        const pathsList = document.createElement('div');
        pathsList.className = 'paths-list';

        paths.forEach((path, index) => {
            const pathElement = document.createElement('div');
            pathElement.className = `path-item ${this.currentPath && this.currentPath.id === path.id ? 'active' : ''} ${path.visible ? 'visible' : 'hidden'}`;

            // 计算可编辑点数量
            const editablePoints = path.points.filter(p => p.flag === 1).length;
            const totalPoints = path.points.length;

            pathElement.innerHTML = `
                <div class="path-header">
                    <div class="path-indicator" style="background: ${path.color}"></div>
                    <div class="path-info">
                        <div class="path-name-row">
                            <span class="path-name">${path.name}</span>
                           
                        </div>
                      
                    </div>
                    <div class="path-actions">
                        <button class="path-action-btn visibility-btn ${path.visible ? 'active' : ''}"
                                onclick="pathManager.togglePathVisibility('${path.id}')"
                                title="${path.visible ? '隐藏' : '显示'}路径">
                            <i class="fas fa-eye${path.visible ? '' : '-slash'}"></i>
                        </button>
                        <button class="path-action-btn video-btn ${path.status === 'opt0' ? 'disabled' : ''}"
                                ${path.status === 'opt0' ? 'disabled' : `onclick="event.stopPropagation(); showPathVideoModal('${path.id}')"`}
                                title="${path.status === 'opt0' ? '原始路径不支持视频生成' : '查看路径视频'}">
                            <i class="fas fa-video"></i>
                        </button>
                        <button class="path-action-btn edit-btn"
                                onclick="pathManager.setCurrentPath('${path.id}')"
                                title="选择编辑路径">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                </div>

                ${this.currentPath && this.currentPath.id === path.id ? this.generatePointsList(path) : ''}
            `;

            // 点击路径项选择路径
            pathElement.addEventListener('click', (e) => {
                if (!e.target.closest('.path-actions')) {
                    this.setCurrentPath(path.id);
                }
            });

            pathsList.appendChild(pathElement);
        });

        pointsContainer.appendChild(pathsList);
    },

    // 生成点列表
    generatePointsList(path) {
        return `
            <div class="points-list">
                <div class="points-header">
                    <span class="points-title">
                        <i class="fas fa-list"></i>路径点详情
                    </span>
                    <span class="points-count">${path.points.length} 个点</span>
                </div>
                <div class="points-container">
                    ${path.points.map((point, index) => `
                        <div class="point-item ${point.flag === 1 ? 'editable' : 'locked'}"
                             onclick="pathManager.selectPoint('${path.id}', ${point.id})">
                            <div class="point-indicator">
                                <div class="point-dot" style="background: ${point.flag === 1 ? path.color : '#666'}"></div>
                                <span class="point-number">${index + 1}</span>
                            </div>
                            <div class="point-info">
                                <div class="point-coords">
                                    <span class="coord-label">坐标:</span>
                                    <span class="coord-values">
                                        (${point.x.toFixed(1)}, ${point.y.toFixed(1)}, ${(point.z || 0).toFixed(1)},${point.angle}°)
                                    </span>
                                </div>
                                <div class="point-details">
                            
                                   
                                    <span class="point-status ${point.flag === 1 ? 'editable' : 'locked'}">
                                      
                                        ${point.flag === 1 ? '可编辑' : '锁定'}
                                    </span>
                                </div>
                            </div>
                            <div class="point-actions">
                                ${point.flag === 1 ? `
                                    <button class="point-action-btn" onclick="event.stopPropagation(); showPointEditModal(pathManager.getCurrentPaths().find(p => p.id === '${path.id}').points.find(pt => pt.id === ${point.id}))" title="编辑点">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                ` : `
                                    <button class="point-action-btn disabled" title="此点已锁定">
                                        <i class="fas fa-lock"></i>
                                    </button>
                                `}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    },

    // 选择点
    selectPoint(pathId, pointId) {
        const paths = this.getCurrentPaths();
        const path = paths.find(p => p.id === pathId);
        if (!path) return;

        const point = path.points.find(p => p.id === pointId);
        if (!point || point.flag !== 1) return;

        // 在3D场景中高亮显示该点
        this.pathObjects.forEach(pathObj => {
            pathObj.points.forEach(pointObj => {
                if (pointObj.point.id === pointId) {
                    pointObj.mesh.material.color.set(0xff9800);
                    selectedPoint = point;

                    // 2秒后恢复原色
                    setTimeout(() => {
                        if (selectedPoint === point) {
                            pointObj.mesh.material.color.set(path.color);
                            selectedPoint = null;
                        }
                    }, 2000);
                }
            });
        });
    },

    // 获取样本类型标签
    getSampleTypeLabel(type) {
        const labels = {
            'urban': '城市道路',
            'highway': '高速公路',
            'intersection': '复杂路口'
        };
        return labels[type] || type;
    },

    // 获取难度标签
    getDifficultyLabel(difficulty) {
        const labels = {
            'easy': '简单',
            'medium': '中等',
            'hard': '困难'
        };
        return labels[difficulty] || difficulty;
    },

    // 获取路径类型标签
    getPathTypeLabel(type) {
        const typeLabels = {
            'primary': '主路径',
            'express': '快速',
            'alternative': '备选',
            'straight': '直行',
            'right_turn': '右转',
            'left_turn': '左转',
            'bridge_up': '上桥',
            'overtake': '超车',
            'backup': '备用',
            'emergency': '紧急'
        };
        return typeLabels[type] || type;
    },

    // 根据样本数据动态调整相机位置
    adjustCameraToSampleData(sampleData) {
        if (!sampleData || !sampleData.paths || sampleData.paths.length === 0) {
            console.warn('样本数据为空，使用默认相机位置');
            return;
        }

        // 收集所有路径点的坐标
        const allPoints = [];
        sampleData.paths.forEach(path => {
            if (path.points && path.points.length > 0) {
                path.points.forEach(point => {
                    allPoints.push({
                        x: point.x,
                        y: point.y,
                        z: point.z || 0
                    });
                });
            }
        });

        if (allPoints.length === 0) {
            console.warn('没有找到有效的路径点，使用默认相机位置');
            return;
        }

        // 计算边界框
        const bounds = this.calculateBounds(allPoints);
        console.log('路径数据边界:', bounds);

        // 计算中心点
        const center = {
            x: (bounds.min.x + bounds.max.x) / 2,
            y: (bounds.min.y + bounds.max.y) / 2,
            z: (bounds.min.z + bounds.max.z) / 2
        };

        // 计算场景尺寸
        const size = {
            x: bounds.max.x - bounds.min.x,
            y: bounds.max.y - bounds.min.y,
            z: bounds.max.z - bounds.min.z
        };

        // 计算合适的相机距离（基于场景尺寸）
        const maxSize = Math.max(size.x, size.y, size.z);
        const distance = Math.max(maxSize * 2, 50); // 至少50单位距离

        // 设置相机位置（从右前方观察）
        const cameraOffset = {
            x: distance * 0.7,  // 右侧
            y: distance * 0.5,  // 前方
            z: distance * 0.3   // 稍微上方
        };

        const newCameraPosition = {
            x: center.x + cameraOffset.x,
            y: center.y + cameraOffset.y,
            z: center.z + cameraOffset.z
        };

        // 更新相机位置和目标
        if (camera) {
            camera.position.set(newCameraPosition.x, newCameraPosition.y, newCameraPosition.z);
            camera.lookAt(center.x, center.y, center.z);

            // 更新OrbitControls的目标点
            if (controls) {
                controls.target.set(center.x, center.y, center.z);
                controls.update();
            }

            // 更新网格辅助线位置
            this.updateGridPosition(center);

            console.log('相机位置已调整:', {
                position: newCameraPosition,
                target: center,
                bounds: bounds,
                distance: distance
            });
        }
    },

    // 计算点集的边界框
    calculateBounds(points) {
        if (points.length === 0) {
            return {
                min: { x: 0, y: 0, z: 0 },
                max: { x: 0, y: 0, z: 0 }
            };
        }

        const bounds = {
            min: {
                x: points[0].x,
                y: points[0].y,
                z: points[0].z
            },
            max: {
                x: points[0].x,
                y: points[0].y,
                z: points[0].z
            }
        };

        points.forEach(point => {
            bounds.min.x = Math.min(bounds.min.x, point.x);
            bounds.min.y = Math.min(bounds.min.y, point.y);
            bounds.min.z = Math.min(bounds.min.z, point.z);

            bounds.max.x = Math.max(bounds.max.x, point.x);
            bounds.max.y = Math.max(bounds.max.y, point.y);
            bounds.max.z = Math.max(bounds.max.z, point.z);
        });

        return bounds;
    },

    // 更新网格辅助线位置
    updateGridPosition(center) {
        // 查找场景中的网格辅助线
        const gridHelper = scene.children.find(child => child.type === 'GridHelper');
        if (gridHelper) {
            gridHelper.position.set(center.x, center.y, 0); // 网格保持在z=0平面
            console.log('网格辅助线位置已更新:', gridHelper.position);
        }
    },

    // 更新图层选择器
    updateLayerSelector() {
        const layerSelect = document.getElementById('layer-select');
        const layerColor = document.getElementById('layer-color');

        if (!layerSelect || !layerColor) return;

        // 清空并重新填充选项
        layerSelect.innerHTML = '';

        if (this.currentSample) {
            const paths = this.getCurrentPaths();
            paths.forEach(path => {
                const option = document.createElement('option');
                option.value = path.id;
                option.textContent = path.name;
                option.dataset.color = path.color;
                layerSelect.appendChild(option);
            });

            // 添加显示全部选项
            const allOption = document.createElement('option');
            allOption.value = 'all';
            allOption.textContent = '显示全部';
            allOption.dataset.color = '#ffffff';
            layerSelect.appendChild(allOption);

            // 设置当前选中的路径
            if (this.currentPath) {
                layerSelect.value = this.currentPath.id;
                layerColor.style.background = this.currentPath.color;
            } else {
                layerSelect.value = 'all';
                layerColor.style.background = '#ffffff';
            }
        }
    },

    // 更新样本信息
    updateSampleInfo() {
        if (!this.currentSample) return;

        // 更新统计数据
        const statValues = document.querySelectorAll('.stat-value');
        if (statValues.length >= 3) {
            statValues[0].textContent = this.currentSample.totalPaths;
            statValues[1].textContent = this.currentSample.estimatedTime;
            statValues[2].textContent = this.getVisiblePaths().length;
        }

        // 更新标签
        const statLabels = document.querySelectorAll('.stat-label');
        if (statLabels.length >= 3) {
            statLabels[0].textContent = '路径数量';
            statLabels[1].textContent = '预计时间';
            statLabels[2].textContent = '可见路径';
        }
    },

    // 更新点坐标
    updatePoint(pathId, pointId, newData) {
        const paths = this.getCurrentPaths();
        const path = paths.find(p => p.id === pathId);
        if (!path) return;

        const point = path.points.find(p => p.id === pointId);
        if (point) {
            Object.assign(point, newData);
            this.renderAllPaths();
            this.updateUI();
        }
    },

    // 删除点
    deletePoint(pathId, pointId) {
        const paths = this.getCurrentPaths();
        const path = paths.find(p => p.id === pathId);
        if (!path) return;

        const pointIndex = path.points.findIndex(p => p.id === pointId);
        if (pointIndex !== -1) {
            path.points.splice(pointIndex, 1);
            this.renderAllPaths();
            this.updateUI();
        }
    }
};

// ThreeJS 初始化
function initThreeJS() {
    console.log('开始初始化ThreeJS...');

    const processedVideoContainer = document.getElementById('processed-video-container');
    if (!processedVideoContainer) {
        console.error('找不到processed-video-container元素');
        return;
    }

    console.log('找到容器元素，开始设置3D场景');

    // 清空容器
    processedVideoContainer.innerHTML = '';

    const canvasContainer = document.createElement('div');
    canvasContainer.className = 'canvas-container';
    canvasContainer.style.width = '100%';
    canvasContainer.style.height = '100%';
    canvasContainer.style.position = 'relative';
    processedVideoContainer.appendChild(canvasContainer);

    // 模式指示器将在工具栏中创建，这里不再添加

    // 检查ThreeJS是否可用
    if (typeof THREE === 'undefined') {
        console.error('ThreeJS库未加载');
        return;
    }

    // 设置场景
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x050e1a);
    console.log('场景创建完成');

    // 获取容器尺寸
    const containerWidth = canvasContainer.clientWidth || 800;
    const containerHeight = canvasContainer.clientHeight || 600;
    console.log(`容器尺寸: ${containerWidth} x ${containerHeight}`);

    // 设置相机 - 根据API数据坐标范围调整
    camera = new THREE.PerspectiveCamera(75, containerWidth / containerHeight, 0.001, 2000);
    // API数据坐标范围大约在 x: -200~-190, y: -40~-15, z: 25~40
    // 调整相机位置以更好地观察路径，更近距离显示
    camera.position.set(-180, -10, 50); // 更近的观察位置，更好的角度
    camera.lookAt(-195, -25, 30); // 看向路径的大致中心
    console.log('相机设置完成，位置:', camera.position);

    // 设置渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(containerWidth, containerHeight);
    renderer.setClearColor(0x050e1a);
    canvasContainer.appendChild(renderer.domElement);
    console.log('渲染器设置完成');

    // 设置OrbitControls控制器
    controls = new THREE.OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true; // 启用阻尼效果
    controls.dampingFactor = 0.05; // 阻尼系数
    controls.screenSpacePanning = false; // 禁用屏幕空间平移
    controls.enablePan = true; // 启用平移功能
    controls.panSpeed = 1.0; // 平移速度
    controls.mouseButtons = {
        LEFT: THREE.MOUSE.ROTATE,   // 左键旋转
        MIDDLE: THREE.MOUSE.DOLLY,  // 中键缩放
        RIGHT: THREE.MOUSE.PAN      // 右键平移
    };
    controls.minDistance = 1; // 最小缩放距离 - 允许更近距离观察
    controls.maxDistance = 2000; // 最大缩放距离 - 允许更远距离观察
    controls.maxPolarAngle = Math.PI; // 移除垂直旋转限制，允许全方位旋转
    controls.target.set(-195, -25, 30); // 设置控制器目标点
    console.log('OrbitControls控制器设置完成 - 左键旋转，右键平移，滚轮缩放');

    // 添加网格辅助线 - 调整大小和位置以适应API数据坐标范围
    const gridHelper = new THREE.GridHelper(100, 20, 0x1e88e5, 0x0a1929);
    gridHelper.position.set(-195, -25, 0); // 移动网格到路径中心附近
   // scene.add(gridHelper);
    console.log('网格辅助线添加完成，位置:', gridHelper.position);

    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);

    // 添加方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(5, 5, 5);
    scene.add(directionalLight);
    console.log('光照设置完成');

    // 添加一个测试立方体来验证渲染
    // const testGeometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
    // const testMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000 });
    // const testCube = new THREE.Mesh(testGeometry, testMaterial);
    // testCube.position.set(0, 0, 0);
    // scene.add(testCube);
    // console.log('测试立方体添加完成');

    // 事件监听器
    renderer.domElement.addEventListener('mousedown', onMouseDown);
    renderer.domElement.addEventListener('mousemove', onMouseMove);
    renderer.domElement.addEventListener('mouseup', onMouseUp);
    renderer.domElement.addEventListener('dblclick', onDoubleClick);
    renderer.domElement.addEventListener('wheel', onMouseWheel);
    renderer.domElement.addEventListener('contextmenu', (e) => e.preventDefault());

    // 窗口大小调整
    window.addEventListener('resize', onWindowResize);

    // 开始动画循环
    animate();
    console.log('ThreeJS初始化完成，开始渲染循环');

    // 初始化路径粗细控制
    initPathThicknessControl();
}

// 动画循环
function animate() {
    requestAnimationFrame(animate);

    // 更新控制器
    if (controls) {
        controls.update();
    }

    if (renderer && scene && camera) {
        renderer.render(scene, camera);
    }
}

// 窗口大小调整
function onWindowResize() {
    const canvasContainer = renderer.domElement.parentElement;
    if (!canvasContainer) return;

    camera.aspect = canvasContainer.clientWidth / canvasContainer.clientHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(canvasContainer.clientWidth, canvasContainer.clientHeight);
}

// 鼠标交互事件
function onMouseDown(event) {
    event.preventDefault();

    const rect = renderer.domElement.getBoundingClientRect();
    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    raycaster.setFromCamera(mouse, camera);

    // 检查与点的交集
    const pointMeshes = [];
    pathManager.pathObjects.forEach(pathObj => {
        pathObj.points.forEach(pointObj => {
            pointMeshes.push(pointObj.mesh);
        });
    });

    const intersects = raycaster.intersectObjects(pointMeshes);

    if (intersects.length > 0) {
        const mesh = intersects[0].object;
        const point = mesh.userData.point;

        if (point.flag === 1) { // 只有可编辑的点才能被选中
            selectedPoint = point;
            // 选中时使用橙色高亮
            mesh.material.color.set(0xff9800);

            if (editMode === 'move') {
                isDragging = true;
            } else {
                isRotating = true;
            }
        } else {
            // 不可编辑的点，短暂闪烁红色提示
            const originalColor = mesh.material.color.getHex();
            mesh.material.color.set(0xff5252);
            setTimeout(() => {
                mesh.material.color.set(originalColor);
            }, 300);
        }
    }
}

function onMouseMove(event) {
    if (!selectedPoint) return;

    const rect = renderer.domElement.getBoundingClientRect();
    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    raycaster.setFromCamera(mouse, camera);

    if (isDragging) {
        // 移动点
        const plane = new THREE.Plane(new THREE.Vector3(0, 0, 1), 0);
        const planeIntersect = new THREE.Vector3();
        raycaster.ray.intersectPlane(plane, planeIntersect);

        selectedPoint.x = planeIntersect.x;
        selectedPoint.y = planeIntersect.y;

        pathManager.renderAllPaths();
    } else if (isRotating) {
        // 旋转点
        const plane = new THREE.Plane(new THREE.Vector3(0, 0, 1), 0);
        const planeIntersect = new THREE.Vector3();
        raycaster.ray.intersectPlane(plane, planeIntersect);

        const dx = planeIntersect.x - selectedPoint.x;
        const dy = planeIntersect.y - selectedPoint.y;
        const angle = Math.atan2(dy, dx) * (180 / Math.PI);

        let newAngle = angle + 90;
        if (newAngle < 0) newAngle += 360;
        if (newAngle >= 360) newAngle -= 360;

        selectedPoint.angle = Math.round(newAngle);
        pathManager.renderAllPaths();
    }
}

function onMouseUp(event) {
    if (selectedPoint) {
        // 重置颜色 - 根据flag决定颜色
        pathManager.pathObjects.forEach(pathObj => {
            pathObj.points.forEach(pointObj => {
                if (pointObj.point === selectedPoint) {
                    // 找到点所属的路径
                    const parentPath = pathManager.getCurrentPaths().find(p =>
                        p.points.some(point => point.id === selectedPoint.id)
                    );
                    if (parentPath) {
                        // flag=1使用路径颜色，flag=0使用灰色
                        const resetColor = selectedPoint.flag === 1 ? parentPath.color : '#cccccc';
                        pointObj.mesh.material.color.set(resetColor);
                    }
                }
            });
        });
    }

    isDragging = false;
    isRotating = false;
    selectedPoint = null;
}

function onDoubleClick(event) {
    event.preventDefault();

    const rect = renderer.domElement.getBoundingClientRect();
    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    raycaster.setFromCamera(mouse, camera);

    const pointMeshes = [];
    pathManager.pathObjects.forEach(pathObj => {
        pathObj.points.forEach(pointObj => {
            pointMeshes.push(pointObj.mesh);
        });
    });

    const intersects = raycaster.intersectObjects(pointMeshes);

    if (intersects.length > 0) {
        const mesh = intersects[0].object;
        const point = mesh.userData.point;

        if (point.flag === 1) {
            // 只有可编辑的点才能打开编辑模态框
            showPointEditModal(point);
        } else {
            // 不可编辑的点，短暂闪烁红色提示
            const originalColor = mesh.material.color.getHex();
            mesh.material.color.set(0xff5252);
            setTimeout(() => {
                mesh.material.color.set(originalColor);
            }, 300);
        }
    }
}

function onMouseWheel(event) {
    // 只处理选中点的角度调整，相机缩放由OrbitControls处理
    if (selectedPoint && selectedPoint.flag === 1) {
        event.preventDefault();
        // 调整角度
        const delta = Math.sign(event.deltaY);
        selectedPoint.angle = (selectedPoint.angle + delta + 360) % 360;
        pathManager.renderAllPaths();
    }
    // 其他情况让OrbitControls处理
}

// 切换编辑模式
function toggleEditMode() {
    editMode = editMode === 'move' ? 'rotate' : 'move';
    const modeIndicator = document.getElementById('mode-indicator');

    if (modeIndicator) {
        modeIndicator.textContent = editMode === 'move' ? '移动模式 (M)' : '旋转模式 (M)';
        modeIndicator.style.color = editMode === 'move' ? 'var(--accent-color)' : '#ff9800';
    }
}

// 样本选择功能
function setupSampleSelection() {
    const sampleButton = document.getElementById('sample-button');

    if (sampleButton) {
        sampleButton.addEventListener('click', () => {
            showSampleSelectionModal();
        });
    }
}

// 显示样本选择模态框 - 支持异步加载
async function showSampleSelectionModal() {
    try {
        const modal = await createSampleModal();
        document.body.appendChild(modal);

        setTimeout(() => {
            modal.style.display = 'flex';
        }, 10);
    } catch (error) {
        console.error('显示样本选择模态框失败:', error);
        showNotification('无法打开样本选择窗口', 'error');
    }
}

// 创建样本选择模态框 - 支持异步加载
async function createSampleModal() {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'sample-selection-modal';

    // 先显示加载状态
    modal.innerHTML = `
        <div class="modal-content-enhanced">
            <div class="modal-header">
                <div class="modal-icon">
                    <i class="fas fa-database"></i>
                </div>
                <div>
                    <h2 class="modal-title">样本选择</h2>
                    <p class="modal-subtitle">正在加载样本数据...</p>
                </div>
                <button class="close-button-enhanced" onclick="closeSampleModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="sample-grid" id="sample-grid">
                    <div class="loading-samples">
                        <div class="loading-spinner"></div>
                        <p>正在获取样本数据...</p>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeSampleModal()">
                    <i class="fas fa-times"></i>取消
                </button>
                <button type="button" class="btn-primary" id="confirm-sample-btn" disabled onclick="confirmSampleSelection()">
                    <i class="fas fa-check"></i>确认选择
                </button>
            </div>
        </div>
    `;

    // 异步加载样本数据
    try {
        const sampleCards = await generateSampleCards();
        const sampleGrid = modal.querySelector('#sample-grid');
        const subtitle = modal.querySelector('.modal-subtitle');

        sampleGrid.innerHTML = sampleCards;
        subtitle.textContent = '选择路径规划样本数据';

        setupSampleModalEvents(modal);
    } catch (error) {
        console.error('加载样本数据失败:', error);
        const sampleGrid = modal.querySelector('#sample-grid');
        sampleGrid.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <p>加载样本数据失败</p>
                <button class="btn-primary-small" onclick="location.reload()">重新加载</button>
            </div>
        `;
    }

    return modal;
}

// 生成样本卡片 - 支持API数据
async function generateSampleCards() {
    try {
        const samples = await dataManager.getSamples();

        if (!samples || samples.length === 0) {
            return `
                <div class="no-samples-message">
                    <div class="no-samples-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3>暂无可用样本</h3>
                    <p>请检查网络连接或联系管理员</p>
                </div>
            `;
        }

        return samples.map(sample => `
            <div class="sample-card" data-sample="${sample.id}">
                <div class="sample-preview">
                    ${sample.image ?
                        `<img src="${image_host}${sample.image}" alt="${sample.name}" class="sample-image">` :
                        '<i class="fas fa-route"></i>'
                    }
                </div>
                <div class="sample-info">
                    <h3>${sample.name}</h3>
                    <p>${sample.remark || '路径规划样本'}</p>
                    <div class="sample-stats">
                        <span><i class="fas fa-calendar"></i> ${formatDate(sample.create_time)}</span>
                        <span><i class="fas fa-clock"></i> ${formatDate(sample.update_time)}</span>
                    </div>
                </div>
                <div class="sample-status">
                    <span class="status-badge status-active">可用</span>
                </div>
            </div>
        `).join('');
    } catch (error) {
        console.error('生成样本卡片失败:', error);
        return `
            <div class="error-message">
                <div class="error-icon">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <h3>加载失败</h3>
                <p>无法获取样本数据: ${error.message}</p>
                <button class="btn-primary-small" onclick="location.reload()">重新加载</button>
            </div>
        `;
    }
}

// 格式化时间戳
function formatDate(timestamp) {
    if (!timestamp) return '未知';
    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString('zh-CN');
}

// 获取难度标签
function getDifficultyLabel(difficulty) {
    const labels = {
        'easy': '简单',
        'medium': '中等',
        'hard': '困难'
    };
    return labels[difficulty] || difficulty;
}

// 获取类型标签
function getTypeLabel(type) {
    const labels = {
        'urban': '城市',
        'highway': '高速',
        'intersection': '路口'
    };
    return labels[type] || type;
}

// 设置样本模态框事件
function setupSampleModalEvents(modal) {
    setTimeout(() => {
        const sampleCards = modal.querySelectorAll('.sample-card');
        const confirmButton = modal.querySelector('#confirm-sample-btn');

        sampleCards.forEach(card => {
            card.addEventListener('click', () => {
                sampleCards.forEach(c => c.classList.remove('selected'));
                card.classList.add('selected');
                confirmButton.disabled = false;
            });
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeSampleModal();
            }
        });
    }, 50);
}

// 关闭样本模态框
function closeSampleModal() {
    const modal = document.getElementById('sample-selection-modal');
    if (modal) {
        modal.style.display = 'none';
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// 确认样本选择 - 支持API数据
async function confirmSampleSelection() {
    const selectedCards = document.querySelectorAll('.sample-card.selected');
    if (selectedCards.length === 0) {
        showNotification('请先选择一个样本', 'warning');
        return;
    }

    const selectedSampleId = selectedCards[0].dataset.sample;

    try {
        // 显示加载状态
        const confirmBtn = document.getElementById('confirm-sample-btn');
        const originalText = confirmBtn.innerHTML;
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>加载中...';
        confirmBtn.disabled = true;

        // 设置当前样本（这会触发API调用）
        await pathManager.setCurrentSample(selectedSampleId);

        closeSampleModal();
    } catch (error) {
        console.error('选择样本失败:', error);
        showNotification('选择样本失败: ' + error.message, 'error');

        // 恢复按钮状态
        const confirmBtn = document.getElementById('confirm-sample-btn');
        if (confirmBtn) {
            confirmBtn.innerHTML = '<i class="fas fa-check"></i>确认选择';
            confirmBtn.disabled = false;
        }
    }
}

// 显示点编辑模态框
function showPointEditModal(point) {
    // 找到点所属的路径
    const paths = pathManager.getCurrentPaths();
    const parentPath = paths.find(path => path.points.includes(point));
    if (!parentPath) return;

    const modal = createPointEditModal(point, parentPath);
    document.body.appendChild(modal);

    setTimeout(() => {
        modal.style.display = 'flex';
    }, 10);
}

// 创建点编辑模态框
function createPointEditModal(point, path) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'point-edit-modal';

    modal.innerHTML = `
        <div class="modal-content-enhanced">
            <div class="modal-header">
                <div class="modal-icon">
                    <i class="fas fa-edit"></i>
                </div>
                <div>
                    <h2 class="modal-title">编辑路径点</h2>
                    <p class="modal-subtitle">修改 ${path.name} - 点 ${point.id} 的坐标和属性</p>
                </div>
                <button class="close-button-enhanced" onclick="closePointEditModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <form id="point-edit-form" class="point-edit-form">
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-map-marker-alt"></i>坐标设置
                        </h3>
                        <div class="form-grid">
                            <div class="form-group-enhanced">
                                <label class="form-label">X坐标 (米)</label>
                                <input type="number" class="form-input-enhanced" id="point-x"
                                       value="${point.x}" step="0.1" min="-10" max="10">
                            </div>
                            <div class="form-group-enhanced">
                                <label class="form-label">Y坐标 (米)</label>
                                <input type="number" class="form-input-enhanced" id="point-y"
                                       value="${point.y}" step="0.1" min="-10" max="10">
                            </div>
                            <div class="form-group-enhanced">
                                <label class="form-label">Z坐标 (米)</label>
                                <input type="number" class="form-input-enhanced" id="point-z"
                                       value="${point.z || 0}" step="0.1" min="-5" max="5">
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-compass"></i>方向设置
                        </h3>
                        <div class="angle-input-group">
                            <div class="form-group-enhanced">
                                <label class="form-label">角度 (度)</label>
                                <div class="angle-input-wrapper">
                                    <input type="number" class="form-input-enhanced angle-input"
                                           id="point-angle" value="${point.angle}"
                                           min="0" max="360" step="1">
                                    <div class="angle-slider-wrapper">
                                        <input type="range" class="angle-slider-enhanced"
                                               id="angle-slider" value="${point.angle}"
                                               min="0" max="360" step="1">
                                    </div>
                                </div>
                            </div>
                            <div class="angle-preview">
                                <div class="angle-compass">
                                    <div class="angle-arrow" id="angle-arrow"
                                         style="transform: rotate(${point.angle}deg)"></div>
                                </div>
                                <div class="angle-value" id="angle-value">${point.angle}°</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-tachometer-alt"></i>速度设置
                        </h3>
                        <div class="form-group-enhanced">
                            <label class="form-label">速度 (km/h)</label>
                            <input type="number" class="form-input-enhanced" id="point-speed"
                                   value="${point.speed || 30}" min="5" max="120" step="5">
                        </div>
                    </div>
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closePointEditModal()">
                    <i class="fas fa-times"></i>取消
                </button>
                <button type="button" class="btn-danger" onclick="deleteCurrentPoint()">
                    <i class="fas fa-trash"></i>删除点
                </button>
                <button type="button" class="btn-primary" onclick="savePointChanges()">
                    <i class="fas fa-save"></i>保存修改
                </button>
            </div>
        </div>
    `;

    setupPointEditEvents(modal, point, path);
    return modal;
}

// 设置点编辑事件
function setupPointEditEvents(modal, point, path) {
    setTimeout(() => {
        const angleInput = modal.querySelector('#point-angle');
        const angleSlider = modal.querySelector('#angle-slider');
        const angleArrow = modal.querySelector('#angle-arrow');
        const angleValue = modal.querySelector('#angle-value');

        // 角度输入同步
        function updateAngle(value) {
            const angle = Math.max(0, Math.min(360, parseFloat(value) || 0));
            angleInput.value = angle;
            angleSlider.value = angle;
            angleArrow.style.transform = `rotate(${angle}deg)`;
            angleValue.textContent = `${angle}°`;
        }

        angleInput.addEventListener('input', (e) => updateAngle(e.target.value));
        angleSlider.addEventListener('input', (e) => updateAngle(e.target.value));

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closePointEditModal();
            }
        });

        // 存储当前编辑的点和路径
        window.currentEditingPoint = point;
        window.currentEditingPath = path;
    }, 50);
}

// 关闭点编辑模态框
function closePointEditModal() {
    const modal = document.getElementById('point-edit-modal');
    if (modal) {
        modal.style.display = 'none';
        setTimeout(() => {
            modal.remove();
            window.currentEditingPoint = null;
            window.currentEditingPath = null;
        }, 300);
    }
}

// 保存点修改
function savePointChanges() {
    const point = window.currentEditingPoint;
    const path = window.currentEditingPath;
    if (!point || !path) return;

    const modal = document.getElementById('point-edit-modal');
    const x = parseFloat(modal.querySelector('#point-x').value);
    const y = parseFloat(modal.querySelector('#point-y').value);
    const z = parseFloat(modal.querySelector('#point-z').value);
    const angle = parseFloat(modal.querySelector('#point-angle').value);
    const speed = parseFloat(modal.querySelector('#point-speed').value);

    // 验证输入
    if (isNaN(x) || isNaN(y) || isNaN(z) || isNaN(angle) || isNaN(speed)) {
        showNotification('请输入有效的数值', 'error');
        return;
    }

    // 更新点数据
    pathManager.updatePoint(path.id, point.id, {
        x: x,
        y: y,
        z: z,
        angle: angle,
        speed: speed
    });

    showNotification('路径点已更新', 'success');
    closePointEditModal();
}

// 删除当前点
function deleteCurrentPoint() {
    const point = window.currentEditingPoint;
    const path = window.currentEditingPath;
    if (!point || !path) return;

    if (confirm(`确定要删除 ${path.name} 中的点 ${point.id} 吗？`)) {
        pathManager.deletePoint(path.id, point.id);
        showNotification('路径点已删除', 'success');
        closePointEditModal();
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// 编辑模式管理
let currentEditMode = 'move'; // 'move' 或 'rotate'

function setEditMode(mode) {
    currentEditMode = mode;
    updateModeIndicator();
    updateToolbarButtons();
}

function toggleEditMode() {
    currentEditMode = currentEditMode === 'move' ? 'rotate' : 'move';
    updateModeIndicator();
    updateToolbarButtons();
}

function updateModeIndicator() {
    const indicator = document.getElementById('mode-indicator');
    if (!indicator) return;

    const icon = indicator.querySelector('.mode-icon i');
    const name = indicator.querySelector('.mode-name');

    if (currentEditMode === 'move') {
        icon.className = 'fas fa-arrows-alt';
        name.textContent = '移动模式';
        indicator.style.background = 'rgba(6, 182, 212, 0.1)';
        indicator.style.borderColor = 'rgba(6, 182, 212, 0.3)';
    } else {
        icon.className = 'fas fa-sync-alt';
        name.textContent = '旋转模式';
        indicator.style.background = 'rgba(139, 92, 246, 0.1)';
        indicator.style.borderColor = 'rgba(139, 92, 246, 0.3)';
    }
}

function updateToolbarButtons() {
    // 更新工具栏按钮状态
    const moveBtn = document.querySelector('[data-tool="move"]');
    const rotateBtn = document.querySelector('[data-tool="rotate"]');

    if (moveBtn && rotateBtn) {
        moveBtn.classList.toggle('active', currentEditMode === 'move');
        rotateBtn.classList.toggle('active', currentEditMode === 'rotate');
    }
}

// 键盘事件处理
function setupKeyboardEvents() {
    window.addEventListener('keydown', (e) => {
        // Escape键关闭模态框
        if (e.key === 'Escape') {
            closeSampleModal();
            closePointEditModal();
        }

        // M键切换编辑模式
        if (e.key.toLowerCase() === 'm') {
            toggleEditMode();
        }

        // Delete键删除选中的点
        if (e.key === 'Delete' && selectedPoint && selectedPoint.flag === 1) {
            const paths = pathManager.getCurrentPaths();
            const parentPath = paths.find(path => path.points.includes(selectedPoint));
            if (parentPath && confirm(`确定要删除点 ${selectedPoint.id} 吗？`)) {
                pathManager.deletePoint(parentPath.id, selectedPoint.id);
                selectedPoint = null;
            }
        }

        // 方向键微调角度
        if (selectedPoint && selectedPoint.flag === 1) {
            if (e.key === 'ArrowLeft') {
                selectedPoint.angle = (selectedPoint.angle - 1 + 360) % 360;
                pathManager.renderAllPaths();
            }
            if (e.key === 'ArrowRight') {
                selectedPoint.angle = (selectedPoint.angle + 1) % 360;
                pathManager.renderAllPaths();
            }
        }
    });
}

// 图层选择器事件
function setupLayerSelector() {
    const layerSelect = document.getElementById('layer-select');
    if (layerSelect) {
        layerSelect.addEventListener('change', function() {
            const selectedValue = this.value;

            if (selectedValue === 'all') {
                // 显示所有路径
                const paths = pathManager.getCurrentPaths();
                paths.forEach(path => {
                    path.visible = true;
                });
                pathManager.renderAllPaths();
                pathManager.updateUI();
            } else {
                // 只显示选中的路径
                const paths = pathManager.getCurrentPaths();
                paths.forEach(path => {
                    path.visible = path.id === selectedValue;
                });
                pathManager.setCurrentPath(selectedValue);
                pathManager.renderAllPaths();
                pathManager.updateUI();
            }
        });
    }
}

// 全屏功能
function setupFullscreenFeature() {
    // 主全屏按钮
    const fullscreenToggle = document.getElementById('fullscreen-toggle');
    if (fullscreenToggle) {
        fullscreenToggle.addEventListener('click', toggleFullscreen);
    }

    // 面板全屏按钮
    const fullscreenBtns = document.querySelectorAll('.fullscreen-btn');
    fullscreenBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            const target = e.currentTarget.dataset.target;
            if (target) {
                togglePanelFullscreen(target);
            }
        });
    });

    // 监听全屏状态变化
    document.addEventListener('fullscreenchange', onFullscreenChange);
    document.addEventListener('webkitfullscreenchange', onFullscreenChange);
    document.addEventListener('mozfullscreenchange', onFullscreenChange);
    document.addEventListener('MSFullscreenChange', onFullscreenChange);
}

// 切换全屏
function toggleFullscreen() {
    if (!document.fullscreenElement &&
        !document.webkitFullscreenElement &&
        !document.mozFullScreenElement &&
        !document.msFullscreenElement) {
        // 进入全屏
        const elem = document.documentElement;
        if (elem.requestFullscreen) {
            elem.requestFullscreen();
        } else if (elem.webkitRequestFullscreen) {
            elem.webkitRequestFullscreen();
        } else if (elem.mozRequestFullScreen) {
            elem.mozRequestFullScreen();
        } else if (elem.msRequestFullscreen) {
            elem.msRequestFullscreen();
        }
    } else {
        // 退出全屏
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
    }
}

// 切换面板全屏
function togglePanelFullscreen(targetId) {
    const target = document.getElementById(targetId);
    if (!target) return;

    if (target.classList.contains('fullscreen-container')) {
        // 退出面板全屏
        target.classList.remove('fullscreen-container');
        document.body.style.overflow = '';
    } else {
        // 进入面板全屏
        target.classList.add('fullscreen-container');
        document.body.style.overflow = 'hidden';

        // 如果是3D工作台，需要调整渲染器尺寸
        if (targetId === 'processed-video-container' && renderer) {
            setTimeout(() => {
                onWindowResize();
            }, 100);
        }
    }
}

// 全屏状态变化处理
function onFullscreenChange() {
    const fullscreenBtn = document.getElementById('fullscreen-toggle');
    if (!fullscreenBtn) return;

    const icon = fullscreenBtn.querySelector('i');
    if (document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement) {
        // 已进入全屏
        icon.className = 'fas fa-compress';
        fullscreenBtn.title = '退出全屏';
    } else {
        // 已退出全屏
        icon.className = 'fas fa-expand';
        fullscreenBtn.title = '全屏';
    }
}

// 帮助菜单功能
function setupHelpMenu() {
    const helpBtn = document.querySelector('[data-dropdown="help"]');
    const helpDropdown = document.getElementById('help-dropdown');

    if (helpBtn && helpDropdown) {
        helpBtn.addEventListener('click', (e) => {
            e.stopPropagation();

            // 关闭其他下拉菜单
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== helpDropdown) {
                    menu.classList.remove('show');
                }
            });

            // 切换帮助菜单
            helpDropdown.classList.toggle('show');
        });

        // 点击菜单项
        const helpItems = helpDropdown.querySelectorAll('.dropdown-item');
        helpItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const action = item.dataset.action;
                handleHelpAction(action);
                helpDropdown.classList.remove('show');
            });
        });
    }

    // 点击外部关闭菜单
    document.addEventListener('click', () => {
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.classList.remove('show');
        });
    });
}

// 处理帮助菜单动作
function handleHelpAction(action) {
    switch (action) {
        case 'shortcuts':
            showShortcutsModal();
            break;
        case 'guide':
            showOperationGuideModal();
            break;
        case 'about':
            showAboutModal();
            break;
        default:
            console.log('未知的帮助动作:', action);
    }
}

// 显示快捷键模态框
function showShortcutsModal() {
    const modal = createShortcutsModal();
    document.body.appendChild(modal);

    setTimeout(() => {
        modal.style.display = 'flex';
    }, 10);
}

// 创建快捷键模态框
function createShortcutsModal() {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'shortcuts-modal';

    modal.innerHTML = `
        <div class="modal-content-enhanced">
            <div class="modal-header">
                <div class="modal-icon">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div>
                    <h2 class="modal-title">键盘快捷键</h2>
                    <p class="modal-subtitle">提高操作效率的快捷键组合</p>
                </div>
                <button class="close-button-enhanced" onclick="closeModal('shortcuts-modal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="guide-sections">
                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-mouse-pointer"></i>编辑操作
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <kbd>M</kbd>
                                <span>切换移动/旋转模式</span>
                            </div>
                            <div class="guide-item">
                                <kbd>Delete</kbd>
                                <span>删除选中的点</span>
                            </div>
                            <div class="guide-item">
                                <kbd>←/→</kbd>
                                <span>微调选中点的角度</span>
                            </div>
                            <div class="guide-item">
                                <kbd>双击</kbd>
                                <span>打开点编辑界面</span>
                            </div>
                        </div>
                    </div>

                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-eye"></i>视图控制
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <kbd>滚轮</kbd>
                                <span>缩放视图/调整角度</span>
                            </div>
                            <div class="guide-item">
                                <kbd>F11</kbd>
                                <span>切换全屏模式</span>
                            </div>
                            <div class="guide-item">
                                <kbd>Esc</kbd>
                                <span>关闭模态框</span>
                            </div>
                        </div>
                    </div>

                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-palette"></i>点状态说明
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <div class="guide-color" style="background: var(--accent-color)"></div>
                                <span>可编辑点 (flag=1)</span>
                            </div>
                            <div class="guide-item">
                                <div class="guide-color" style="background: #cccccc"></div>
                                <span>锁定点 (flag=0)</span>
                            </div>
                            <div class="guide-item">
                                <div class="guide-color" style="background: #ff9800"></div>
                                <span>选中状态</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeModal('shortcuts-modal')">
                    <i class="fas fa-times"></i>关闭
                </button>
            </div>
        </div>
    `;

    setupModalEvents(modal);
    return modal;
}

// 显示操作指南模态框
function showOperationGuideModal() {
    const modal = createOperationGuideModal();
    document.body.appendChild(modal);

    setTimeout(() => {
        modal.style.display = 'flex';
    }, 10);
}

// 创建操作指南模态框
function createOperationGuideModal() {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'operation-guide-modal';

    modal.innerHTML = `
        <div class="modal-content-enhanced">
            <div class="modal-header">
                <div class="modal-icon">
                    <i class="fas fa-book-open"></i>
                </div>
                <div>
                    <h2 class="modal-title">操作指南</h2>
                    <p class="modal-subtitle">路径编辑工作台使用说明</p>
                </div>
                <button class="close-button-enhanced" onclick="closeModal('operation-guide-modal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="guide-sections">
                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-play-circle"></i>快速开始
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <span>1. 点击"样本选择"按钮选择路径数据</span>
                            </div>
                            <div class="guide-item">
                                <span>2. 在左侧路径列表中管理路径显示</span>
                            </div>
                            <div class="guide-item">
                                <span>3. 在3D工作台中编辑路径点</span>
                            </div>
                            <div class="guide-item">
                                <span>4. 使用双击打开详细编辑界面</span>
                            </div>
                        </div>
                    </div>

                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-edit"></i>编辑功能
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <span>• 拖拽移动：左键拖拽可编辑的点</span>
                            </div>
                            <div class="guide-item">
                                <span>• 角度调整：切换到旋转模式或使用滚轮</span>
                            </div>
                            <div class="guide-item">
                                <span>• 详细编辑：双击点打开编辑界面</span>
                            </div>
                            <div class="guide-item">
                                <span>• 删除点：选中后按Delete键</span>
                            </div>
                        </div>
                    </div>

                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-layer-group"></i>路径管理
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <span>• 显示/隐藏：点击眼睛图标控制路径可见性</span>
                            </div>
                            <div class="guide-item">
                                <span>• 选择路径：点击路径项或编辑按钮</span>
                            </div>
                            <div class="guide-item">
                                <span>• 图层选择：使用状态栏的下拉选择器</span>
                            </div>
                            <div class="guide-item">
                                <span>• 路径信息：查看右上角的路径图例</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeModal('operation-guide-modal')">
                    <i class="fas fa-times"></i>关闭
                </button>
            </div>
        </div>
    `;

    setupModalEvents(modal);
    return modal;
}

// 显示关于模态框
function showAboutModal() {
    const modal = createAboutModal();
    document.body.appendChild(modal);

    setTimeout(() => {
        modal.style.display = 'flex';
    }, 10);
}

// 创建关于模态框
function createAboutModal() {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'about-modal';

    modal.innerHTML = `
        <div class="modal-content-enhanced">
            <div class="modal-header">
                <div class="modal-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div>
                    <h2 class="modal-title">关于系统</h2>
                    <p class="modal-subtitle">世界模型可视化展示大屏</p>
                </div>
                <button class="close-button-enhanced" onclick="closeModal('about-modal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="guide-sections">
                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-rocket"></i>系统信息
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <span><strong>版本:</strong> v1.0.0</span>
                            </div>
                            <div class="guide-item">
                                <span><strong>技术栈:</strong> ThreeJS + JavaScript</span>
                            </div>
                            <div class="guide-item">
                                <span><strong>功能:</strong> 3D路径编辑与可视化</span>
                            </div>
                            <div class="guide-item">
                                <span><strong>更新时间:</strong> 2025年6月</span>
                            </div>
                        </div>
                    </div>

                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-star"></i>主要特性
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <span>• 多路径同时显示和编辑</span>
                            </div>
                            <div class="guide-item">
                                <span>• 实时3D可视化渲染</span>
                            </div>
                            <div class="guide-item">
                                <span>• 直观的交互式编辑界面</span>
                            </div>
                            <div class="guide-item">
                                <span>• 灵活的路径管理系统</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeModal('about-modal')">
                    <i class="fas fa-times"></i>关闭
                </button>
            </div>
        </div>
    `;

    setupModalEvents(modal);
    return modal;
}

// 通用模态框事件设置
function setupModalEvents(modal) {
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    });
}

// 关闭模态框
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// 显示视频生成模态框
function showVideoGenerationModal(pathId) {
    const path = pathManager.getCurrentPaths().find(p => p.id === pathId);
    if (!path) return;

    const modal = createVideoGenerationModal(path);
    document.body.appendChild(modal);

    setTimeout(() => {
        modal.style.display = 'flex';
    }, 10);
}

// 创建视频生成模态框
function createVideoGenerationModal(path) {
    const modal = document.createElement('div');
    modal.className = 'modal video-generation-modal';
    modal.id = 'video-generation-modal';

    modal.innerHTML = `
        <div class="modal-content-enhanced video-modal-content">
            <div class="modal-header">
                <div class="modal-icon">
                    <i class="fas fa-video"></i>
                </div>
                <div>
                    <h2 class="modal-title">视频生成</h2>
                    <p class="modal-subtitle">基于路径 "${path.name}" 生成预测视频</p>
                </div>
                <div class="modal-header-actions">
                    <button class="modal-action-btn" onclick="toggleVideoModalFullscreen()" title="全屏/退出全屏" id="video-modal-fullscreen-btn">
                        <i class="fas fa-expand"></i>
                    </button>
                    <button class="close-button-enhanced" onclick="closeModal('video-generation-modal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <div class="modal-body video-modal-body">
                <div class="video-generation-content">
                    <!-- 路径信息 -->
                    <div class="path-info-section">
                        <h3 class="section-title">
                            <i class="fas fa-route"></i>路径信息
                        </h3>
                        <div class="path-details-grid">
                            <div class="detail-item">
                                <span class="detail-label">路径名称:</span>
                                <span class="detail-value">${path.name}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">路径类型:</span>
                                <span class="detail-value">${pathManager.getPathTypeLabel(path.type)}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">点数量:</span>
                                <span class="detail-value">${path.points.length} 个点</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">可编辑点:</span>
                                <span class="detail-value">${path.points.filter(p => p.flag === 1).length} 个</span>
                            </div>
                        </div>
                    </div>

                    <!-- 视频对比区域 -->
                    <div class="video-comparison-section">
                        <h3 class="section-title">
                            <i class="fas fa-film"></i>视频对比
                        </h3>
                        <div class="video-comparison-grid">
                            <!-- 原视频 -->
                            <div class="video-panel">
                                <div class="video-header">
                                    <h4 class="video-title">
                                        <i class="fas fa-video"></i>原始视频
                                    </h4>
                                    <div class="video-controls">
                                        <button class="video-control-btn" onclick="toggleVideo('original-video')" title="播放/暂停">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="video-control-btn" onclick="toggleVideoFullscreen('original-video')" title="全屏">
                                            <i class="fas fa-expand"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="video-container">
                                    <video id="original-video" class="comparison-video" loop muted loop="loop">
                                        <source src="./1.mp4" type="video/mp4">
                                        您的浏览器不支持视频播放。
                                    </video>
                                    <div class="video-overlay">
                                        <div class="video-info">
                                            <span class="video-label">原始数据</span>
                                            <span class="video-duration">2:30</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 预测视频 -->
                            <div class="video-panel">
                                <div class="video-header">
                                    <h4 class="video-title">
                                        <i class="fas fa-magic"></i>预测视频
                                    </h4>
                                    <div class="video-controls">
                                        <button class="video-control-btn" onclick="toggleVideo('predicted-video')" title="播放/暂停">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button class="video-control-btn" onclick="toggleVideoFullscreen('predicted-video')" title="全屏">
                                            <i class="fas fa-expand"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="video-container">
                                    <div class="video-placeholder" id="predicted-video-placeholder">
                                        <div class="placeholder-content">
                                            <div class="placeholder-icon">
                                                <i class="fas fa-cog fa-spin"></i>
                                            </div>
                                            <h4>正在生成预测视频</h4>
                                            <p>基于路径数据生成AI预测视频...</p>
                                            <div class="generation-progress">
                                                <div class="progress-bar">
                                                    <div class="progress-fill" id="generation-progress"></div>
                                                </div>
                                                <span class="progress-text">0%</span>
                                            </div>
                                        </div>
                                    </div>
                                    <video id="predicted-video" class="comparison-video" loop muted style="display: none;" loop="loop">
                                        <source src="./1.mp4" type="video/mp4">
                                        您的浏览器不支持视频播放。
                                    </video>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 生成选项 -->
                    <div class="generation-options-section">
                        <h3 class="section-title">
                            <i class="fas fa-sliders-h"></i>生成选项
                        </h3>
                        <div class="options-grid">
                            <div class="option-group">
                                <label class="option-label">视频质量</label>
                                <select class="option-select" id="video-quality">
                                    <option value="720p">720p (推荐)</option>
                                    <option value="1080p">1080p (高质量)</option>
                                    <option value="480p">480p (快速)</option>
                                </select>
                            </div>
                            <div class="option-group">
                                <label class="option-label">帧率</label>
                                <select class="option-select" id="video-fps">
                                    <option value="30">30 FPS</option>
                                    <option value="60">60 FPS</option>
                                    <option value="24">24 FPS</option>
                                </select>
                            </div>
                            <div class="option-group">
                                <label class="option-label">视频长度</label>
                                <select class="option-select" id="video-duration">
                                    <option value="auto">自动 (基于路径)</option>
                                    <option value="30">30秒</option>
                                    <option value="60">60秒</option>
                                    <option value="120">2分钟</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeModal('video-generation-modal')">
                    <i class="fas fa-times"></i>关闭
                </button>
                <button type="button" class="btn-primary" id="start-generation-btn" onclick="startVideoGeneration('${path.id}')">
                    <i class="fas fa-play"></i>开始生成
                </button>
                <button type="button" class="btn-primary" id="download-video-btn" onclick="downloadGeneratedVideo()" style="display: none;">
                    <i class="fas fa-download"></i>下载视频
                </button>
            </div>
        </div>
    `;

    setupModalEvents(modal);
    return modal;
}

// 视频控制功能
function toggleVideo(videoId) {
    const video = document.getElementById(videoId);
    if (!video) return;

    const btn = video.closest('.video-panel').querySelector('.video-control-btn i');

    if (video.paused) {
        video.play();
        btn.className = 'fas fa-pause';
    } else {
        video.pause();
        btn.className = 'fas fa-play';
    }
}

function toggleVideoFullscreen(videoId) {
    const video = document.getElementById(videoId);
    if (!video) return;

    if (video.requestFullscreen) {
        video.requestFullscreen();
    } else if (video.webkitRequestFullscreen) {
        video.webkitRequestFullscreen();
    } else if (video.mozRequestFullScreen) {
        video.mozRequestFullScreen();
    } else if (video.msRequestFullscreen) {
        video.msRequestFullscreen();
    }
}

// 开始视频生成
function startVideoGeneration(pathId) {
    const startBtn = document.getElementById('start-generation-btn');
    const downloadBtn = document.getElementById('download-video-btn');
    const progressBar = document.getElementById('generation-progress');
    const progressText = document.querySelector('.progress-text');
    const placeholder = document.getElementById('predicted-video-placeholder');
    const video = document.getElementById('predicted-video');

    // 禁用开始按钮
    startBtn.disabled = true;
    startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>生成中...';

    // 模拟生成进度
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 100) progress = 100;

        progressBar.style.width = progress + '%';
        progressText.textContent = Math.round(progress) + '%';

        if (progress >= 100) {
            clearInterval(progressInterval);

            // 生成完成
            setTimeout(() => {
                placeholder.style.display = 'none';
                video.style.display = 'block';

                // 恢复按钮状态
                startBtn.style.display = 'none';
                downloadBtn.style.display = 'inline-flex';

                // 显示成功消息
                showNotification('视频生成完成！', 'success');
            }, 1000);
        }
    }, 200);
}

// 生成预测视频
async function generatePredictedVideo(pathId) {
    try {
        const path = pathManager.getCurrentPaths().find(p => p.id === pathId);
        if (!path) {
            showNotification('路径不存在', 'error');
            return;
        }

        // 显示生成进度
        showNotification('开始生成预测视频...', 'info');

        // 更新按钮状态
        const generateBtn = document.querySelector(`button[onclick="generatePredictedVideo('${pathId}')"]`);
        if (generateBtn) {
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            generateBtn.disabled = true;
        }

        // 调用API生成视频
        const result = await apiManager.generatePredictedVideo(pathId);

        if (result.success) {
            // 更新路径的视频URL
            path.video = result.videoUrl;

            // 图例已删除，无需更新

            showNotification('预测视频生成成功！', 'success');
        } else {
            throw new Error(result.message || '生成失败');
        }

    } catch (error) {
        console.error('生成预测视频失败:', error);
        showNotification('生成预测视频失败: ' + error.message, 'error');

        // 恢复按钮状态
        const generateBtn = document.querySelector(`button[onclick="generatePredictedVideo('${pathId}')"]`);
        if (generateBtn) {
            generateBtn.innerHTML = '<i class="fas fa-cog"></i>';
            generateBtn.disabled = false;
        }
    }
}

// 显示单个路径的视频模态框
function showPathVideoModal(pathId) {
    const sample = pathManager.getCurrentSample();
    if (!sample) {
        showNotification('请先选择一个样本', 'warning');
        return;
    }

    const selectedPath = sample.paths.find(p => p.id === pathId);
    if (!selectedPath) {
        showNotification('未找到指定路径', 'error');
        return;
    }

    // 获取原始路径（通常是第一个路径或标记为原始的路径）
    const originalPath = sample.paths.find(p => p.status === 'opt0') || sample.paths[0];

    const modal = createPathVideoModal(originalPath, selectedPath);
    document.body.appendChild(modal);

    // 显示模态框
    setTimeout(() => {
        modal.style.display = 'flex';
    }, 10);
}

// 创建路径视频对比模态框
function createPathVideoModal(originalPath, selectedPath) {
    const modal = document.createElement('div');
    modal.className = 'modal path-video-modal';
    modal.id = 'path-video-modal';

    modal.innerHTML = `
        <div class="modal-content-enhanced">
            <div class="modal-header-modern">
                <div class="header-left">
                    <div class="modal-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <div>
                        <h2 class="modal-title">路径视频对比</h2>
                        <p class="modal-subtitle">原始视频 vs ${selectedPath.name}</p>
                    </div>
                </div>
                <div class="modal-header-actions">
                    <button class="modal-action-btn" onclick="togglePathVideoModalFullscreen()" title="全屏/退出全屏" id="path-video-modal-fullscreen-btn">
                        <i class="fas fa-expand"></i>
                    </button>
                    <button class="close-button-enhanced" onclick="closeModal('path-video-modal')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <div class="modal-body-modern">
                <div class="video-comparison-grid">
                    <!-- 原始视频卡片 -->
                    <div class="video-card original-card">
                        <div class="video-card-header">
                            <div class="video-title">
                                <i class="fas fa-play-circle"></i>
                                <span>原始视频</span>
                            </div>
                            <div class="video-badge ${originalPath && originalPath.video ? 'badge-success' : 'badge-error'}">
                                ${originalPath && originalPath.video ? '可播放' : '不可用'}
                            </div>
                        </div>
                        <div class="video-card-body">
                            ${originalPath && originalPath.video ?
                                `<div class="video-wrapper">
                                    <video controls class="video-player" preload="metadata" loop="loop">
                                        <source src="${image_host}${originalPath.video}" type="video/mp4">
                                        您的浏览器不支持视频播放。
                                    </video>                                    
                                </div>` :
                                `<div class="video-placeholder">
                                    <div class="placeholder-icon">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="placeholder-text">
                                        <h4>原视频不可用</h4>
                                        <p>该样本暂无原始视频数据</p>
                                    </div>
                                </div>`
                            }
                        </div>
                        <div class="video-card-footer">
                            <div class="video-info">
                                <span class="info-item">
                                    <i class="fas fa-tag"></i>
                                    原始数据
                                </span>
                                ${originalPath ?
                                    `<span class="info-item">
                                        <i class="fas fa-route"></i>
                                        ${originalPath.points.length} 个路径点
                                    </span>` : ''
                                }
                            </div>
                        </div>
                    </div>

                    <!-- 选中路径视频卡片 -->
                    <div class="video-card predicted-card" data-path-id="${selectedPath.id}">
                        <div class="video-card-header">
                            <div class="video-title">
                                <i class="fas fa-robot"></i>
                                <span>${selectedPath.name}</span>
                            </div>
                            <div class="video-badge badge-pending" id="video-badge-${selectedPath.id}">
                                待生成
                            </div>
                        </div>
                        <div class="video-card-body">
                            <!-- 预测视频始终显示占位符，需要用户点击生成 -->
                            <div class="video-placeholder" id="path-video-placeholder-${selectedPath.id}">
                                <div class="placeholder-icon">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <div class="placeholder-text">
                                    <h4>AI预测视频</h4>
                                    <p>点击下方"算法生成"按钮生成该路径的预测视频</p>
                                </div>
                            </div>
                        </div>
                        <div class="video-card-footer">
                            <div class="video-info">
                                <span class="info-item">
                                    <i class="fas fa-route"></i>
                                    ${selectedPath.points.length} 个路径点
                                </span>
                                <span class="info-item">
                                    <i class="fas fa-clock"></i>
                                    预计时长: ${Math.ceil(selectedPath.points.length * 0.5)}s
                                </span>
                            </div>
                            <div class="video-actions">
                                <!-- 始终显示算法生成按钮 -->
                                <button class="btn-primary generate-video-btn" onclick="generatePathVideo('${selectedPath.id}')" id="generate-btn-${selectedPath.id}">
                                    <i class="fas fa-magic"></i>
                                    算法生成
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn-primary" onclick="closeModal('path-video-modal')">
                    <i class="fas fa-times"></i>关闭
                </button>
               
            </div>
        </div>
    `;

    setupModalEvents(modal);
    return modal;
}

// 生成路径视频
async function generatePathVideo(pathId) {
    const sample = pathManager.getCurrentSample();
    if (!sample) {
        showNotification('请先选择一个样本', 'warning');
        return;
    }

    const path = sample.paths.find(p => p.id === pathId);
    console.log(pathId)

    console.log(path);
    if (!path) {
        showNotification('未找到指定路径', 'error');
        return;
    }

    const generateBtn = document.getElementById(`generate-btn-${pathId}`);
    const placeholder = document.getElementById(`path-video-placeholder-${pathId}`);

    if (!generateBtn) {
        showNotification('未找到生成按钮', 'error');
        return;
    }

    try {
        // 禁用按钮并显示生成状态
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>生成中...';

        // 更新占位符显示生成进度
        if (placeholder) {
            placeholder.innerHTML = `
                <div class="placeholder-icon">
                    <i class="fas fa-cog fa-spin"></i>
                </div>
                <div class="placeholder-text">
                    <h4>正在生成视频...</h4>
                    <p>算法生成中，预计需要 3-10 秒</p>
                </div>
                <div class="generation-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill-${pathId}"></div>
                    </div>
                    <div class="progress-text" id="progress-text-${pathId}">0%</div>
                    <div class="progress-time" id="progress-time-${pathId}">剩余时间: 计算中...</div>
                </div>
            `;
        }

        // 生成随机的生成时间（3-10秒）
        const generationTime = Math.random() * 7000 + 3000; // 3000-10000ms
        const startTime = Date.now();

        // 模拟生成进度
        let progress = 0;
        const progressInterval = setInterval(() => {
            const elapsed = Date.now() - startTime;
            const progressPercent = Math.min((elapsed / generationTime) * 100, 95);

            // 添加一些随机性让进度条更自然
            progress = Math.min(progressPercent + Math.random() * 5, 95);

            const progressFill = document.getElementById(`progress-fill-${pathId}`);
            const progressText = document.getElementById(`progress-text-${pathId}`);
            const progressTime = document.getElementById(`progress-time-${pathId}`);

            if (progressFill) progressFill.style.width = `${progress}%`;
            if (progressText) progressText.textContent = `${Math.round(progress)}%`;

            // 计算剩余时间
            if (progressTime && progress < 95) {
                const remainingTime = Math.max(0, generationTime - elapsed);
                const remainingSeconds = Math.ceil(remainingTime / 1000);
                progressTime.textContent = `剩余时间: ${remainingSeconds} 秒`;
            }
        }, 300);

        // 等待最小生成时间完成
        const minWaitTime = Math.max(0, generationTime - (Date.now() - startTime));
        if (minWaitTime > 0) {
            await new Promise(resolve => setTimeout(resolve, minWaitTime));
        }

        // 调用API生成视频
        // const response = await fetch('https://word.sszhai.com/api/index/simples', {
        //     method: 'POST',
        //     headers: {
        //         'Content-Type': 'application/json',
        //     },
        //     body: JSON.stringify({
        //         server: 1,
        //         id: sample.id
               
        //     })
        // });

        const result = path;

        // 清除进度条
        clearInterval(progressInterval);

        if (result && result.video) {
            // 更新路径数据
            path.video = result.video;

            // 完成进度条动画
            const progressFill = document.getElementById(`progress-fill-${pathId}`);
            const progressText = document.getElementById(`progress-text-${pathId}`);
            const progressTime = document.getElementById(`progress-time-${pathId}`);

            if (progressFill) progressFill.style.width = '100%';
            if (progressText) progressText.textContent = '100%';
            if (progressTime) progressTime.textContent = '生成完成！';

            // 短暂延迟后显示完成状态
            setTimeout(() => {
                // 更新视频卡片显示
                const videoCard = document.querySelector(`[data-path-id="${pathId}"]`);
                if (videoCard) {
                    const cardBody = videoCard.querySelector('.video-card-body');
                    const badge = videoCard.querySelector('.video-badge');

                    if (cardBody) {
                        // 显示生成成功的视频
                        cardBody.innerHTML = `
                            <div class="video-wrapper">
                                <video controls class="video-player" preload="metadata" poster="" loop="loop">
                                    <source src="${image_host}${path.video}" type="video/mp4">
                                    您的浏览器不支持视频播放。
                                </video>
                              
                                <div class="video-success-indicator">
                                    <i class="fas fa-check-circle"></i>
                                    <span>生成成功</span>
                                </div>
                            </div>
                        `;

                        // 自动加载视频元数据
                        const video = cardBody.querySelector('video');
                        if (video) {
                            video.addEventListener('loadedmetadata', () => {
                                console.log(`视频 ${path.name} 加载完成，时长: ${video.duration}秒`);
                            });
                        }
                    }

                    if (badge) {
                        badge.className = 'video-badge badge-success';
                        badge.textContent = '已生成';
                    }
                }

                // 更新按钮状态
                generateBtn.disabled = false;
                generateBtn.className = 'btn-secondary regenerate-video-btn';
                generateBtn.innerHTML = '<i class="fas fa-redo"></i>重新生成';
                generateBtn.setAttribute('onclick', `generatePathVideo('${pathId}')`);

                showNotification(`路径 "${path.name}" 视频生成成功！`, 'success');
            }, 800);

        } else {
            throw new Error(result.message || '生成失败');
        }

    } catch (error) {
        console.error('生成路径视频失败:', error);
        showNotification('生成视频失败: ' + error.message, 'error');

        // 恢复按钮状态
        generateBtn.disabled = false;
        generateBtn.innerHTML = '<i class="fas fa-magic"></i>算法生成';

        // 恢复占位符
        if (placeholder) {
            placeholder.innerHTML = `
                <div class="placeholder-icon">
                    <i class="fas fa-video-slash"></i>
                </div>
                <div class="placeholder-text">
                    <h4>生成失败</h4>
                    <p>请重试或联系管理员</p>
                </div>
            `;
        }
    }
}

// 路径视频模态框全屏功能
function togglePathVideoModalFullscreen() {
    const modal = document.getElementById('path-video-modal');
    const fullscreenBtn = document.getElementById('path-video-modal-fullscreen-btn');

    if (!modal || !fullscreenBtn) return;

    const isFullscreen = modal.classList.contains('modal-fullscreen');

    if (isFullscreen) {
        // 退出全屏
        modal.classList.remove('modal-fullscreen');
        fullscreenBtn.querySelector('i').className = 'fas fa-expand';
        fullscreenBtn.title = '全屏';
    } else {
        // 进入全屏
        modal.classList.add('modal-fullscreen');
        fullscreenBtn.querySelector('i').className = 'fas fa-compress';
        fullscreenBtn.title = '退出全屏';
    }
}



// 显示视频对比模态框
function showVideoComparisonModal(sampleId) {
    const currentSample = pathManager.getCurrentSample();
    if (!currentSample) {
        showNotification('请先选择样本', 'warning');
        return;
    }

    // 获取原视频和预测视频路径
    const originalPath = currentSample.paths.find(p => p.status === 'opt0');
    const predictedPaths = currentSample.paths.filter(p => p.status === 'opt1');

    const modal = createVideoComparisonModal(originalPath, predictedPaths, currentSample);
    document.body.appendChild(modal);

    setTimeout(() => {
        modal.style.display = 'flex';
    }, 10);
}

// 创建视频对比模态框
function createVideoComparisonModal(originalPath, predictedPaths, sample) {
    const modal = document.createElement('div');
    modal.className = 'modal video-comparison-modal';
    modal.id = 'video-comparison-modal';

    // 生成预测视频卡片HTML
    const predictedVideosHtml = predictedPaths.map((path) => `
        <div class="video-card predicted-card" data-path-id="${path.id}">
            <div class="video-card-header">
                <div class="video-title">
                    <i class="fas fa-robot"></i>
                    <span>${path.name}</span>
                </div>
                <div class="video-badge ${path.video ? 'badge-success' : 'badge-pending'}">
                    ${path.video ? '已生成' : '待生成'}
                </div>
            </div>
            <div class="video-card-body">
                ${path.video ?
                    `<div class="video-wrapper">
                        <video controls class="video-player" preload="metadata" loop="loop">
                            <source src="${image_host}${path.video}" type="video/mp4">
                            您的浏览器不支持视频播放。
                        </video>
                        
                    </div>` :
                    `<div class="video-placeholder">
                        <div class="placeholder-icon">
                            <i class="fas fa-video-slash"></i>
                        </div>
                        <div class="placeholder-text">
                            <h4>视频未生成</h4>
                            <p>点击下方按钮生成预测视频</p>
                        </div>
                        <button class="generate-btn" onclick="generatePredictedVideoInModal('${path.id}')">
                            <i class="fas fa-magic"></i>
                            <span>模型生成</span>
                        </button>
                    </div>`
                }
            </div>
            <div class="video-card-footer">
                <div class="video-info">
                    <span class="info-item">
                        <i class="fas fa-route"></i>
                        ${path.points.length} 个路径点
                    </span>
                    <span class="info-item">
                        <i class="fas fa-clock"></i>
                        预计时长: ${Math.ceil(path.points.length * 0.5)}s
                    </span>
                </div>
                ${path.video ?
                    `<button class="download-btn" onclick="downloadVideo('${image_host}${path.video}', '${path.name}.mp4')">
                        <i class="fas fa-download"></i>
                    </button>` : ''
                }
            </div>
        </div>
    `).join('');

    modal.innerHTML = `
        <div class="modal-content-enhanced video-comparison-content">
            <div class="modal-header-modern">
                <div class="header-left">
                    <div class="modal-icon-modern">
                        <i class="fas fa-video"></i>
                    </div>
                    <div class="header-text">
                        <h2 class="modal-title-modern">视频对比分析</h2>
                        <p class="modal-subtitle-modern">${sample.name}</p>
                    </div>
                </div>
                <div class="header-right">
                    <div class="video-stats">
                        <span class="stat-item">
                            <i class="fas fa-film"></i>
                            ${originalPath && originalPath.video ? '1' : '0'} 原视频
                        </span>
                        <span class="stat-item">
                            <i class="fas fa-robot"></i>
                            ${predictedPaths.filter(p => p.video).length}/${predictedPaths.length} 预测视频
                        </span>
                    </div>
                    <button class="close-btn-modern" onclick="closeVideoComparisonModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <div class="modal-body-modern">
                <div class="video-comparison-grid">
                    <!-- 原视频卡片 -->
                    <div class="video-card original-card">
                        <div class="video-card-header">
                            <div class="video-title">
                                <i class="fas fa-play-circle"></i>
                                <span>原始视频</span>
                            </div>
                            <div class="video-badge ${originalPath && originalPath.video ? 'badge-success' : 'badge-error'}">
                                ${originalPath && originalPath.video ? '可播放' : '不可用'}
                            </div>
                        </div>
                        <div class="video-card-body">
                            ${originalPath && originalPath.video ?
                                `<div class="video-wrapper">
                                    <video controls class="video-player" preload="metadata" loop="loop">
                                        <source src="${image_host}${originalPath.video}" type="video/mp4">
                                        您的浏览器不支持视频播放。
                                    </video>
                                  
                                </div>` :
                                `<div class="video-placeholder">
                                    <div class="placeholder-icon">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="placeholder-text">
                                        <h4>原视频不可用</h4>
                                        <p>该样本暂无原始视频数据</p>
                                    </div>
                                </div>`
                            }
                        </div>
                        <div class="video-card-footer">
                            <div class="video-info">
                                <span class="info-item">
                                    <i class="fas fa-tag"></i>
                                    原始数据
                                </span>
                                ${originalPath ?
                                    `<span class="info-item">
                                        <i class="fas fa-route"></i>
                                        ${originalPath.points.length} 个路径点
                                    </span>` : ''
                                }
                            </div>
                            ${originalPath && originalPath.video ?
                                `<button class="download-btn" onclick="downloadVideo('${image_host}${originalPath.video}', '原视频.mp4')">
                                    <i class="fas fa-download"></i>
                                </button>` : ''
                            }
                        </div>
                    </div>

                    <!-- 预测视频卡片 -->
                    ${predictedVideosHtml}
                </div>
            </div>

            <div class="modal-footer-modern">
                <div class="footer-left">
                    <button class="btn-modern btn-secondary" onclick="closeVideoComparisonModal()">
                        <i class="fas fa-times"></i>
                        <span>关闭</span>
                    </button>
                </div>
                <div class="footer-right">
                    <button class="btn-modern btn-outline" onclick="toggleVideoComparisonFullscreen()">
                        <i class="fas fa-expand"></i>
                        <span>全屏</span>
                    </button>
                    <button class="btn-modern btn-primary" onclick="downloadAllVideos()">
                        <i class="fas fa-download"></i>
                        <span>下载全部</span>
                    </button>
                </div>
            </div>
        </div>
    `;

    // 设置事件监听
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeVideoComparisonModal();
        }
    });

    return modal;
}

// 关闭视频对比模态框
function closeVideoComparisonModal() {
    const modal = document.getElementById('video-comparison-modal');
    if (modal) {
        const videos = modal.querySelectorAll('video');
        videos.forEach(video => {
            video.pause();
        });
        modal.style.display = 'none';
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// 在模态框中生成预测视频
async function generatePredictedVideoInModal(pathId) {
    try {
        const path = pathManager.getCurrentPaths().find(p => p.id === pathId);
        if (!path) {
            showNotification('路径不存在', 'error');
            return;
        }

        // 更新按钮状态
        const generateBtn = document.querySelector(`button[onclick="generatePredictedVideoInModal('${pathId}')"]`);
        if (generateBtn) {
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>生成中...';
            generateBtn.disabled = true;
        }

        // 显示生成进度
        showNotification('开始生成预测视频...', 'info');

        // 调用API生成视频
        const result = await apiManager.generatePredictedVideo(pathId);

        if (result.success) {
            // 更新路径的视频URL
            path.video = result.videoUrl;

            // 更新模态框中的视频显示
            const videoItem = document.querySelector(`[data-path-id="${pathId}"]`);
            if (videoItem) {
                const videoContainer = videoItem.querySelector('.video-container');
                videoContainer.innerHTML = `
                    <video controls class="video-player" loop="loop">
                        <source src="${image_host}${path.video}" type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                `;

                // 更新状态
                const statusSpan = videoItem.querySelector('.video-status');
                statusSpan.textContent = '已生成';
                statusSpan.className = 'video-status generated';
            }

            // 图例已删除，无需更新

            showNotification('预测视频生成成功！', 'success');
        } else {
            throw new Error(result.message || '生成失败');
        }

    } catch (error) {
        console.error('生成预测视频失败:', error);
        showNotification('生成预测视频失败: ' + error.message, 'error');

        // 恢复按钮状态
        const generateBtn = document.querySelector(`button[onclick="generatePredictedVideoInModal('${pathId}')"]`);
        if (generateBtn) {
            generateBtn.innerHTML = '<i class="fas fa-cog"></i>算法生成';
            generateBtn.disabled = false;
        }
    }
}

// 下载视频
function downloadVideo(videoUrl, filename) {
    const link = document.createElement('a');
    link.href = videoUrl;
    link.download = filename;
    link.click();

    showNotification('视频下载已开始', 'info');
}

// 全屏播放视频
function playVideoFullscreen(videoUrl) {
    const fullscreenModal = document.createElement('div');
    fullscreenModal.className = 'fullscreen-video-modal';
    fullscreenModal.innerHTML = `
        <div class="fullscreen-video-container">
            <video controls autoplay class="fullscreen-video" loop="loop">
                <source src="${videoUrl}" type="video/mp4">
                您的浏览器不支持视频播放。
            </video>
            <button class="fullscreen-close-btn" onclick="closeFullscreenVideo()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    document.body.appendChild(fullscreenModal);

    setTimeout(() => {
        fullscreenModal.style.display = 'flex';
    }, 10);
}

// 关闭全屏视频
function closeFullscreenVideo() {
    const modal = document.querySelector('.fullscreen-video-modal');
    if (modal) {
        const video = modal.querySelector('video');
        if (video) {
            video.pause();
        }
        modal.style.display = 'none';
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// 切换视频对比全屏
function toggleVideoComparisonFullscreen() {
    const modal = document.getElementById('video-comparison-modal');
    if (!modal) return;

    if (modal.requestFullscreen) {
        modal.requestFullscreen();
    } else if (modal.webkitRequestFullscreen) {
        modal.webkitRequestFullscreen();
    } else if (modal.msRequestFullscreen) {
        modal.msRequestFullscreen();
    }
}

// 下载所有视频
function downloadAllVideos() {
    const currentSample = pathManager.getCurrentSample();
    if (!currentSample) {
        showNotification('没有可下载的视频', 'warning');
        return;
    }

    let downloadCount = 0;

    // 下载原视频
    const originalPath = currentSample.paths.find(p => p.status === 'opt0');
    if (originalPath && originalPath.video) {
        downloadVideo(`${image_host}${originalPath.video}`, `${currentSample.name}_原视频.mp4`);
        downloadCount++;
    }

    // 下载预测视频
    const predictedPaths = currentSample.paths.filter(p => p.status === 'opt1' && p.video);
    predictedPaths.forEach((path, index) => {
        downloadVideo(`${image_host}${path.video}`, `${currentSample.name}_预测视频_${index + 1}.mp4`);
        downloadCount++;
    });

    if (downloadCount > 0) {
        showNotification(`开始下载 ${downloadCount} 个视频文件`, 'info');
    } else {
        showNotification('没有可下载的视频', 'warning');
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // 自动隐藏
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// 工作区覆盖层切换功能
function toggleWorkspaceOverlay() {
    const overlay = document.querySelector('.workspace-overlay');
    const toggleBtn = document.getElementById('overlay-toggle');

    if (!overlay || !toggleBtn) return;

    const isVisible = overlay.style.display !== 'none';

    if (isVisible) {
        // 隐藏覆盖层
        overlay.style.display = 'none';
        toggleBtn.classList.add('hidden');
        toggleBtn.querySelector('i').className = 'fas fa-eye-slash';
        toggleBtn.title = '显示路径图例';
    } else {
        // 显示覆盖层
        overlay.style.display = 'block';
        toggleBtn.classList.remove('hidden');
        toggleBtn.querySelector('i').className = 'fas fa-layer-group';
        toggleBtn.title = '隐藏路径图例';
    }
}

// 视频模态框全屏功能
function toggleVideoModalFullscreen() {
    const modal = document.getElementById('video-generation-modal');
    const fullscreenBtn = document.getElementById('video-modal-fullscreen-btn');

    if (!modal || !fullscreenBtn) return;

    const isFullscreen = modal.classList.contains('modal-fullscreen');

    if (isFullscreen) {
        // 退出全屏
        modal.classList.remove('modal-fullscreen');
        fullscreenBtn.querySelector('i').className = 'fas fa-expand';
        fullscreenBtn.title = '全屏';
        document.body.style.overflow = '';
    } else {
        // 进入全屏
        modal.classList.add('modal-fullscreen');
        fullscreenBtn.querySelector('i').className = 'fas fa-compress';
        fullscreenBtn.title = '退出全屏';
        document.body.style.overflow = 'hidden';
    }
}

// 初始化路径粗细控制
function initPathThicknessControl() {
    const thicknessSlider = document.getElementById('path-thickness-slider');
    const thicknessValue = document.getElementById('thickness-value');

    if (!thicknessSlider || !thicknessValue) {
        console.warn('路径粗细控制元素未找到');
        return;
    }

    // 设置初始值
    thicknessSlider.value = pathThickness;
    thicknessValue.textContent = pathThickness.toFixed(1);

    // 监听滑块变化
    thicknessSlider.addEventListener('input', function() {
        pathThickness = parseFloat(this.value);
        thicknessValue.textContent = pathThickness.toFixed(1);

        // 实时更新路径粗细
        updatePathThickness();
    });

    console.log('路径粗细控制初始化完成');
}

// 更新路径粗细
function updatePathThickness() {
    // 重新渲染所有路径以应用新的粗细
    if (pathManager && pathManager.currentSample) {
        pathManager.renderAllPaths();
        console.log('路径粗细已更新为:', pathThickness);
    }
}

// 视图模式切换功能
function switchViewMode(mode) {
    if (mode === currentViewMode) return;

    console.log(`切换视图模式: ${currentViewMode} -> ${mode}`);

    const container3D = document.getElementById('processed-video-container');
    const container2D = document.getElementById('canvas-2d-container');
    const btn3D = document.querySelector('[data-mode="3d"]');
    const btn2D = document.querySelector('[data-mode="2d"]');

    if (!container3D || !container2D || !btn3D || !btn2D) {
        console.error('视图切换元素未找到');
        return;
    }

    // 保存视图模式状态
    stateManager.saveViewMode(mode);
    currentViewMode = mode;

    if (mode === '3d') {
        // 切换到3D视图
        container3D.classList.add('active');
        container2D.classList.remove('active');
        btn3D.classList.add('active');
        btn2D.classList.remove('active');

        // 重新渲染3D场景
        if (pathManager && pathManager.currentSample) {
            pathManager.renderAllPaths();
        }

        showNotification('已切换到3D视图', 'info');
    } else if (mode === '2d') {
        // 切换到2D视图
        container2D.classList.add('active');
        container3D.classList.remove('active');
        btn2D.classList.add('active');
        btn3D.classList.remove('active');

        // 初始化2D画布（如果还没有初始化）
        if (!canvas2D) {
            init2DCanvas();
        }

        // 渲染2D场景
        if (pathManager && pathManager.currentSample) {
            render2DPaths();
        }

        showNotification('已切换到2D视图', 'info');
    }
}

// 初始化2D画布
function init2DCanvas() {
    const container = document.getElementById('canvas-2d-container');
    if (!container) return;

    canvas2D = document.getElementById('canvas-2d');
    if (!canvas2D) {
        console.error('2D画布元素未找到');
        return;
    }

    ctx2D = canvas2D.getContext('2d');

    // 设置画布尺寸
    const resizeCanvas = () => {
        const rect = container.getBoundingClientRect();
        canvas2D.width = rect.width;
        canvas2D.height = rect.height;
        canvas2D.style.display = 'block';

        // 隐藏占位符
        const placeholder = container.querySelector('.scene-placeholder');
        if (placeholder) {
            placeholder.style.display = 'none';
        }

        // 重新渲染
        if (currentViewMode === '2d' && pathManager && pathManager.currentSample) {
            render2DPaths();
        }
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // 添加鼠标事件监听
    canvas2D.addEventListener('click', on2DCanvasClick);
    canvas2D.addEventListener('mousemove', on2DCanvasMouseMove);

    console.log('2D画布初始化完成');
}

// 渲染2D路径
function render2DPaths() {
    if (!ctx2D || !canvas2D || !pathManager.currentSample) return;

    // 清空画布
    ctx2D.clearRect(0, 0, canvas2D.width, canvas2D.height);

    // 绘制网格
    draw2DGrid();

    // 获取所有路径点的边界
    const bounds = calculate2DBounds();
    if (!bounds) return;

    // 计算缩放和偏移
    const scale = calculate2DScale(bounds);
    const offset = calculate2DOffset(bounds, scale);

    // 渲染可见路径
    const visiblePaths = pathManager.getVisiblePaths();
    visiblePaths.forEach(path => {
        render2DPath(path, scale, offset);
    });

    console.log('2D路径渲染完成');
}

// 绘制2D网格
function draw2DGrid() {
    const gridSize = 50;
    ctx2D.strokeStyle = '#333';
    ctx2D.lineWidth = 1;

    // 绘制垂直线
    for (let x = 0; x < canvas2D.width; x += gridSize) {
        ctx2D.beginPath();
        ctx2D.moveTo(x, 0);
        ctx2D.lineTo(x, canvas2D.height);
        ctx2D.stroke();
    }

    // 绘制水平线
    for (let y = 0; y < canvas2D.height; y += gridSize) {
        ctx2D.beginPath();
        ctx2D.moveTo(0, y);
        ctx2D.lineTo(canvas2D.width, y);
        ctx2D.stroke();
    }
}

// 计算2D边界
function calculate2DBounds() {
    const allPoints = [];
    pathManager.getCurrentPaths().forEach(path => {
        path.points.forEach(point => {
            allPoints.push({ x: point.x, y: point.y });
        });
    });

    if (allPoints.length === 0) return null;

    return {
        minX: Math.min(...allPoints.map(p => p.x)),
        maxX: Math.max(...allPoints.map(p => p.x)),
        minY: Math.min(...allPoints.map(p => p.y)),
        maxY: Math.max(...allPoints.map(p => p.y))
    };
}

// 计算2D缩放比例
function calculate2DScale(bounds) {
    const padding = 50;
    const availableWidth = canvas2D.width - padding * 2;
    const availableHeight = canvas2D.height - padding * 2;

    const dataWidth = bounds.maxX - bounds.minX;
    const dataHeight = bounds.maxY - bounds.minY;

    if (dataWidth === 0 || dataHeight === 0) return 1;

    const scaleX = availableWidth / dataWidth;
    const scaleY = availableHeight / dataHeight;

    return Math.min(scaleX, scaleY);
}

// 计算2D偏移
function calculate2DOffset(bounds, scale) {
    const padding = 50;
    const scaledWidth = (bounds.maxX - bounds.minX) * scale;
    const scaledHeight = (bounds.maxY - bounds.minY) * scale;

    return {
        x: (canvas2D.width - scaledWidth) / 2 - bounds.minX * scale,
        y: (canvas2D.height - scaledHeight) / 2 - bounds.minY * scale
    };
}

// 渲染单条2D路径
function render2DPath(path, scale, offset) {
    if (path.points.length === 0) return;

    // 绘制连接线
    if (path.points.length > 1) {
        ctx2D.strokeStyle = path.color;
        ctx2D.lineWidth = pathThickness * 2;
        ctx2D.beginPath();

        const firstPoint = path.points[0];
        ctx2D.moveTo(
            firstPoint.x * scale + offset.x,
            firstPoint.y * scale + offset.y
        );

        for (let i = 1; i < path.points.length; i++) {
            const point = path.points[i];
            ctx2D.lineTo(
                point.x * scale + offset.x,
                point.y * scale + offset.y
            );
        }

        ctx2D.stroke();
    }

    // 绘制路径点
    path.points.forEach((point, index) => {
        const x = point.x * scale + offset.x;
        const y = point.y * scale + offset.y;

        // 绘制点
        ctx2D.fillStyle = point.flag === 1 ? path.color : '#cccccc';
        ctx2D.beginPath();
        ctx2D.arc(x, y, 6, 0, 2 * Math.PI);
        ctx2D.fill();

        // 绘制点编号
        ctx2D.fillStyle = '#ffffff';
        ctx2D.font = '12px Arial';
        ctx2D.textAlign = 'center';
        ctx2D.fillText((index + 1).toString(), x, y + 4);

        // 绘制方向指示器
        if (point.angle !== undefined) {
            const angleRad = (point.angle - 90) * (Math.PI / 180);
            const arrowLength = 20;
            const arrowX = x + Math.cos(angleRad) * arrowLength;
            const arrowY = y + Math.sin(angleRad) * arrowLength;

            ctx2D.strokeStyle = point.flag === 1 ? path.color : '#aaaaaa';
            ctx2D.lineWidth = 2;
            ctx2D.beginPath();
            ctx2D.moveTo(x, y);
            ctx2D.lineTo(arrowX, arrowY);
            ctx2D.stroke();

            // 绘制箭头头部
            const headLength = 8;
            const headAngle = Math.PI / 6;

            ctx2D.beginPath();
            ctx2D.moveTo(arrowX, arrowY);
            ctx2D.lineTo(
                arrowX - headLength * Math.cos(angleRad - headAngle),
                arrowY - headLength * Math.sin(angleRad - headAngle)
            );
            ctx2D.moveTo(arrowX, arrowY);
            ctx2D.lineTo(
                arrowX - headLength * Math.cos(angleRad + headAngle),
                arrowY - headLength * Math.sin(angleRad + headAngle)
            );
            ctx2D.stroke();
        }
    });
}

// 2D画布点击事件
function on2DCanvasClick(event) {
    if (!pathManager.currentSample) return;

    const rect = canvas2D.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const clickY = event.clientY - rect.top;

    // 查找点击的路径点
    const bounds = calculate2DBounds();
    if (!bounds) return;

    const scale = calculate2DScale(bounds);
    const offset = calculate2DOffset(bounds, scale);

    pathManager.getCurrentPaths().forEach(path => {
        path.points.forEach((point, index) => {
            const pointX = point.x * scale + offset.x;
            const pointY = point.y * scale + offset.y;

            const distance = Math.sqrt(
                Math.pow(clickX - pointX, 2) + Math.pow(clickY - pointY, 2)
            );

            if (distance <= 10) { // 点击容差
                console.log(`点击了路径点: ${path.name} - 点 ${index + 1}`);
                showNotification(`选中路径点: ${path.name} - 点 ${index + 1}`, 'info');

                // 高亮显示点击的点
                highlight2DPoint(pointX, pointY);
            }
        });
    });
}

// 高亮2D点
function highlight2DPoint(x, y) {
    ctx2D.strokeStyle = '#ffff00';
    ctx2D.lineWidth = 3;
    ctx2D.beginPath();
    ctx2D.arc(x, y, 10, 0, 2 * Math.PI);
    ctx2D.stroke();

    // 2秒后重新渲染以移除高亮
    setTimeout(() => {
        if (currentViewMode === '2d') {
            render2DPaths();
        }
    }, 2000);
}

// 2D画布鼠标移动事件
function on2DCanvasMouseMove(event) {
    const rect = canvas2D.getBoundingClientRect();
    const mouseX = event.clientX - rect.left;
    const mouseY = event.clientY - rect.top;

    // 更新坐标显示
    const cursorCoords = document.getElementById('cursor-coords');
    if (cursorCoords) {
        cursorCoords.textContent = `坐标: (${mouseX.toFixed(0)}, ${mouseY.toFixed(0)})`;
    }
}

// 视图工具函数
function zoomIn() {
    if (currentViewMode === '3d' && controls) {
        const currentDistance = camera.position.distanceTo(controls.target);
        const newDistance = currentDistance * 0.8;
        const direction = camera.position.clone().sub(controls.target).normalize();
        camera.position.copy(controls.target).add(direction.multiplyScalar(newDistance));
        controls.update();
    } else if (currentViewMode === '2d') {
        // 2D缩放功能可以在这里实现
        showNotification('2D缩放功能开发中...', 'info');
    }
}

function zoomOut() {
    if (currentViewMode === '3d' && controls) {
        const currentDistance = camera.position.distanceTo(controls.target);
        const newDistance = currentDistance * 1.2;
        const direction = camera.position.clone().sub(controls.target).normalize();
        camera.position.copy(controls.target).add(direction.multiplyScalar(newDistance));
        controls.update();
    } else if (currentViewMode === '2d') {
        // 2D缩放功能可以在这里实现
        showNotification('2D缩放功能开发中...', 'info');
    }
}

function fitToView() {
    if (currentViewMode === '3d' && pathManager.currentSample) {
        pathManager.adjustCameraToSampleData(pathManager.currentSample);
    } else if (currentViewMode === '2d') {
        render2DPaths();
    }
}

// 初始化应用
async function init() {
    console.log('开始初始化应用...');

    initThreeJS();
    setupSampleSelection();
    setupKeyboardEvents();
    setupLayerSelector();
    setupFullscreenFeature();
    setupHelpMenu();
    initPathThicknessControl();

    // 恢复视图模式状态
    const savedViewMode = stateManager.loadViewMode();
    if (savedViewMode !== currentViewMode) {
        switchViewMode(savedViewMode);
    }

    // 尝试恢复保存的样本状态
    const restored = await pathManager.restoreFromSavedState();
    if (!restored) {
        console.log('没有保存的样本状态，请点击"样本选择"按钮选择数据');
    }

    console.log('世界模型可视化展示大屏已初始化');
}

// 启动应用
document.addEventListener('DOMContentLoaded', init);
