# 路径视频模态框功能更新

## 更新说明

根据用户需求，修改了路径视频模态框的显示逻辑：

### 修改前的问题
- 如果路径已有视频，右边会直接显示视频
- 用户无法体验算法生成的过程

### 修改后的行为
- **左边**：原始视频正常显示
- **右边**：预测视频区域始终显示占位符，不管是否已有视频
- **用户操作**：必须点击"算法生成"按钮才能生成和显示预测视频

## 具体修改内容

### 1. 预测视频显示逻辑

**修改前**：
```javascript
${selectedPath.video ?
    `<div class="video-wrapper">
        <video controls class="video-player" preload="metadata">
            <source src="${image_host}${selectedPath.video}" type="video/mp4">
        </video>
    </div>` :
    `<div class="video-placeholder">
        <h4>视频未生成</h4>
        <p>点击下方按钮生成预测视频</p>
    </div>`
}
```

**修改后**：
```javascript
<!-- 预测视频始终显示占位符，需要用户点击生成 -->
<div class="video-placeholder" id="path-video-placeholder-${selectedPath.id}">
    <div class="placeholder-icon">
        <i class="fas fa-robot"></i>
    </div>
    <div class="placeholder-text">
        <h4>AI预测视频</h4>
        <p>点击下方"算法生成"按钮生成该路径的预测视频</p>
    </div>
</div>
```

### 2. 视频状态徽章

**修改前**：
```javascript
<div class="video-badge ${selectedPath.video ? 'badge-success' : 'badge-pending'}">
    ${selectedPath.video ? '已生成' : '待生成'}
</div>
```

**修改后**：
```javascript
<div class="video-badge badge-pending" id="video-badge-${selectedPath.id}">
    待生成
</div>
```

### 3. 操作按钮

**修改前**：
```javascript
${!selectedPath.video ? 
    `<button class="btn-primary generate-video-btn">算法生成</button>` :
    `<button class="btn-secondary regenerate-video-btn">重新生成</button>`
}
```

**修改后**：
```javascript
<!-- 始终显示算法生成按钮 -->
<button class="btn-primary generate-video-btn" onclick="generatePathVideo('${selectedPath.id}')" id="generate-btn-${selectedPath.id}">
    <i class="fas fa-magic"></i>
    算法生成
</button>
```

## 用户体验流程

### 1. 打开模态框
- 用户点击路径列表中的视频按钮（📹）
- 模态框弹出，显示左右两个视频区域

### 2. 初始状态
- **左边**：原始视频正常播放
- **右边**：显示AI机器人图标和"AI预测视频"占位符
- **状态徽章**：显示"待生成"
- **按钮**：显示"算法生成"按钮

### 3. 点击生成
- 用户点击"算法生成"按钮
- 按钮变为"生成中..."并禁用
- 占位符显示生成进度条（0-100%）
- 模拟3-5秒的生成时间

### 4. 生成完成
- 进度条达到100%
- 占位符被替换为实际的视频播放器
- 状态徽章变为"已生成"
- 按钮变为"重新生成"
- 显示成功通知

### 5. 重新生成
- 如果用户再次点击按钮（现在显示为"重新生成"）
- 重复步骤3-4的流程

## 技术实现细节

### 占位符样式
```css
.video-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
    border: 2px dashed rgba(255, 255, 255, 0.2);
}

.placeholder-icon {
    font-size: 48px;
    color: var(--accent-color);
    margin-bottom: 16px;
}
```

### 生成进度条
```css
.generation-progress {
    margin-top: 16px;
    width: 100%;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-color), var(--secondary-color));
    transition: width 0.3s ease;
}
```

### API调用
```javascript
const response = await fetch('https://word.sszhai.com/api/index/simples', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        server: 1,
        id: sample.id,
        path_id: pathId,
        action: 'generate_video'
    })
});
```

## 功能特点

### ✅ 强制用户交互
- 预测视频不会自动显示
- 用户必须主动点击生成按钮
- 增强了用户对AI生成过程的感知

### ✅ 清晰的状态反馈
- 占位符明确说明需要用户操作
- 生成过程有实时进度显示
- 按钮状态实时更新

### ✅ 一致的用户体验
- 无论路径是否已有视频，界面表现一致
- 用户每次都能体验完整的生成流程
- 避免了界面状态的不一致性

### ✅ 视觉设计优化
- 使用机器人图标表示AI功能
- 虚线边框暗示可交互区域
- 渐变进度条提供视觉反馈

## 测试场景

1. **首次打开模态框**
   - 验证右边显示占位符
   - 验证按钮显示"算法生成"

2. **点击生成按钮**
   - 验证按钮变为"生成中..."
   - 验证进度条正常显示
   - 验证3-5秒后生成完成

3. **生成完成后**
   - 验证视频正常显示
   - 验证按钮变为"重新生成"
   - 验证状态徽章更新

4. **重新生成**
   - 验证可以重复生成流程
   - 验证每次都有完整的进度显示

## 兼容性

- ✅ 保持原有的API接口不变
- ✅ 保持原有的CSS样式兼容
- ✅ 支持所有现代浏览器
- ✅ 响应式设计适配移动端
