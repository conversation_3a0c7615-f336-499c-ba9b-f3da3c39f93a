# 2D Canvas 缩放功能实现说明

## 功能概述

为 `id="canvas-2d-container"` 中的 canvas 添加了鼠标滚轮缩放功能，支持：
- 鼠标滚轮缩放（以鼠标位置为中心）
- 工具栏按钮缩放（以画布中心为中心）
- 视图重置功能
- 缩放信息显示

## 实现详情

### 1. 全局变量添加

**文件位置**: `static/js/script.js` 第18-23行

```javascript
// 2D画布缩放相关变量
let canvas2DScale = 1;          // 当前缩放比例
let canvas2DOffsetX = 0;        // X轴偏移
let canvas2DOffsetY = 0;        // Y轴偏移
let canvas2DMinScale = 0.1;     // 最小缩放比例
let canvas2DMaxScale = 5;       // 最大缩放比例
```

### 2. 事件监听器添加

**文件位置**: `static/js/script.js` 第3620行

```javascript
// 添加滚轮事件监听
canvas2D.addEventListener('wheel', on2DCanvasWheel, { passive: false });
```

### 3. 滚轮缩放事件处理

**核心函数**: `on2DCanvasWheel(event)`

```javascript
function on2DCanvasWheel(event) {
    event.preventDefault();
    
    if (!pathManager.currentSample) return;

    const rect = canvas2D.getBoundingClientRect();
    const mouseX = event.clientX - rect.left;
    const mouseY = event.clientY - rect.top;

    // 计算缩放因子
    const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;
    const newScale = canvas2DScale * zoomFactor;

    // 限制缩放范围
    if (newScale < canvas2DMinScale || newScale > canvas2DMaxScale) {
        return;
    }

    // 计算缩放中心点
    const worldX = (mouseX - canvas2DOffsetX) / canvas2DScale;
    const worldY = (mouseY - canvas2DOffsetY) / canvas2DScale;

    // 更新缩放比例
    canvas2DScale = newScale;

    // 更新偏移量，使缩放以鼠标位置为中心
    canvas2DOffsetX = mouseX - worldX * canvas2DScale;
    canvas2DOffsetY = mouseY - worldY * canvas2DScale;

    // 重新渲染
    render2DPaths();

    // 显示缩放信息
    showZoomInfo();
}
```

### 4. 渲染系统修改

**修改的函数**: `render2DPaths()`

```javascript
function render2DPaths() {
    if (!ctx2D || !canvas2D || !pathManager.currentSample) return;

    // 清空画布
    ctx2D.clearRect(0, 0, canvas2D.width, canvas2D.height);

    // 保存画布状态
    ctx2D.save();

    // 应用缩放和偏移变换
    ctx2D.translate(canvas2DOffsetX, canvas2DOffsetY);
    ctx2D.scale(canvas2DScale, canvas2DScale);

    // 绘制网格和路径...

    // 恢复画布状态
    ctx2D.restore();
}
```

### 5. 网格系统优化

**修改的函数**: `draw2DGrid()`

```javascript
function draw2DGrid() {
    const baseGridSize = 50;
    const gridSize = baseGridSize / canvas2DScale; // 根据缩放调整网格大小
    
    ctx2D.strokeStyle = canvas2DScale > 1 ? '#444' : '#333';
    ctx2D.lineWidth = 1 / canvas2DScale; // 根据缩放调整线宽

    // 计算可见区域，只绘制可见的网格线
    const startX = Math.floor(-canvas2DOffsetX / canvas2DScale / gridSize) * gridSize;
    const endX = Math.ceil((canvas2D.width - canvas2DOffsetX) / canvas2DScale / gridSize) * gridSize;
    const startY = Math.floor(-canvas2DOffsetY / canvas2DScale / gridSize) * gridSize;
    const endY = Math.ceil((canvas2D.height - canvas2DOffsetY) / canvas2DScale / gridSize) * gridSize;

    // 绘制网格线...
}
```

### 6. 工具栏按钮

**HTML修改**: `index.html` 第230-232行

```html
<button class="toolbar-btn" data-tool="reset-view" title="重置视图" onclick="resetCurrentView()">
    <i class="fas fa-home"></i>
</button>
```

**对应的JavaScript函数**:

```javascript
// 放大功能
function zoomIn() {
    if (currentViewMode === '2d') {
        const zoomFactor = 1.2;
        // 以画布中心为缩放中心
        // ...缩放逻辑
    }
}

// 缩小功能
function zoomOut() {
    if (currentViewMode === '2d') {
        const zoomFactor = 0.8;
        // 以画布中心为缩放中心
        // ...缩放逻辑
    }
}

// 重置视图
function resetCurrentView() {
    if (currentViewMode === '2d') {
        reset2DView();
    } else {
        // 重置3D视图
    }
}
```

### 7. 缩放信息显示

**函数**: `showZoomInfo()`

```javascript
function showZoomInfo() {
    const zoomInfo = document.getElementById('zoom-info');
    if (!zoomInfo) {
        // 动态创建缩放信息显示元素
        const info = document.createElement('div');
        info.id = 'zoom-info';
        info.className = 'zoom-info';
        info.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-family: monospace;
            z-index: 1000;
            pointer-events: none;
        `;
        document.getElementById('canvas-2d-container').appendChild(info);
    }

    const info = document.getElementById('zoom-info');
    info.textContent = `缩放: ${(canvas2DScale * 100).toFixed(0)}%`;
    info.style.opacity = '1';

    // 2秒后淡出
    clearTimeout(info.fadeTimeout);
    info.fadeTimeout = setTimeout(() => {
        info.style.opacity = '0.3';
    }, 2000);
}
```

## 功能特点

### ✅ 鼠标滚轮缩放
- **向上滚动**: 放大 (缩放因子 1.1)
- **向下滚动**: 缩小 (缩放因子 0.9)
- **缩放中心**: 鼠标当前位置
- **缩放范围**: 10% - 500%

### ✅ 工具栏按钮缩放
- **放大按钮**: 以画布中心放大 (缩放因子 1.2)
- **缩小按钮**: 以画布中心缩小 (缩放因子 0.8)
- **重置按钮**: 恢复到初始状态

### ✅ 智能网格系统
- **动态网格大小**: 根据缩放比例调整网格密度
- **性能优化**: 只绘制可见区域的网格线
- **视觉反馈**: 缩放时网格颜色和线宽自适应

### ✅ 缩放信息显示
- **实时显示**: 当前缩放百分比
- **自动淡出**: 2秒后变为半透明
- **位置固定**: 右上角显示

### ✅ 坐标系统
- **世界坐标**: 路径点的实际坐标
- **屏幕坐标**: 经过缩放和偏移后的显示坐标
- **坐标转换**: 正确处理鼠标事件和渲染

## 技术实现细节

### 1. 坐标变换
```javascript
// 屏幕坐标转世界坐标
const worldX = (screenX - canvas2DOffsetX) / canvas2DScale;
const worldY = (screenY - canvas2DOffsetY) / canvas2DScale;

// 世界坐标转屏幕坐标
const screenX = worldX * canvas2DScale + canvas2DOffsetX;
const screenY = worldY * canvas2DScale + canvas2DOffsetY;
```

### 2. Canvas变换
```javascript
// 应用变换
ctx2D.save();
ctx2D.translate(canvas2DOffsetX, canvas2DOffsetY);
ctx2D.scale(canvas2DScale, canvas2DScale);

// 绘制内容...

// 恢复变换
ctx2D.restore();
```

### 3. 性能优化
- **可见区域计算**: 只绘制可见的网格线
- **事件防抖**: 滚轮事件适当的响应频率
- **状态管理**: 使用 save/restore 管理Canvas状态

## 用户体验

### 🎯 直观的缩放操作
- **鼠标滚轮**: 最常用的缩放方式
- **工具栏按钮**: 精确的缩放控制
- **重置功能**: 快速回到初始状态

### 🎯 视觉反馈
- **缩放信息**: 实时显示当前缩放比例
- **网格适应**: 网格密度随缩放自动调整
- **平滑动画**: 缩放过程流畅自然

### 🎯 操作限制
- **缩放范围**: 防止过度缩放导致的问题
- **边界检查**: 确保缩放操作的有效性
- **状态保持**: 切换视图模式时保持缩放状态

## 兼容性

- ✅ 所有现代浏览器
- ✅ 触摸设备（可扩展支持触摸缩放）
- ✅ 高DPI显示器
- ✅ 不同屏幕尺寸

## 扩展性

这个缩放系统可以轻松扩展：

1. **触摸缩放**: 添加触摸事件支持
2. **平移功能**: 添加拖拽平移
3. **缩放动画**: 添加平滑的缩放动画
4. **缩放历史**: 记录缩放操作历史
5. **快捷键**: 添加键盘快捷键支持
