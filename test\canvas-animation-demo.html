<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas 图片序列播放 Demo</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        canvas {
            border: 2px solid #333;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        .control-group {
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        button {
            padding: 10px 20px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 5px;
        }
        
        .play-btn {
            background-color: #4CAF50;
            color: white;
        }
        
        .pause-btn {
            background-color: #f44336;
            color: white;
        }
        
        .reset-btn {
            background-color: #2196F3;
            color: white;
        }
        
        button:hover {
            opacity: 0.8;
        }
        
        input[type="range"] {
            width: 200px;
        }
        
        .status {
            margin: 10px 0;
            font-size: 14px;
            color: #666;
        }
        
        label {
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Canvas 图片序列播放 Demo</h1>
        <p>模拟视频播放效果，循环播放 1-6.png 图片序列</p>
        
        <canvas id="animationCanvas" width="400" height="300"></canvas>
        
        <div class="controls">
            <div class="control-group">
                <button id="playBtn" class="play-btn">播放</button>
                <button id="pauseBtn" class="pause-btn">暂停</button>
                <button id="resetBtn" class="reset-btn">重置</button>
            </div>
            
            <div class="control-group">
                <label for="speedRange">播放速度 (ms):</label>
                <input type="range" id="speedRange" min="100" max="1000" value="300" step="50">
                <span id="speedValue">300ms</span>
            </div>
            
            <div class="control-group">
                <label for="frameRange">当前帧:</label>
                <input type="range" id="frameRange" min="1" max="6" value="1" step="1">
                <span id="frameValue">1/6</span>
            </div>
        </div>
        
        <div class="status">
            <div>状态: <span id="statusText">已停止</span></div>
            <div>当前图片: <span id="currentImage">1.png</span></div>
        </div>
    </div>

    <script>
        class CanvasImageSequence {
            constructor() {
                this.canvas = document.getElementById('animationCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.images = [];
                this.currentFrame = 0;
                this.totalFrames = 6;
                this.isPlaying = false;
                this.animationInterval = null;
                this.frameDelay = 300; // 默认300ms
                
                this.initializeElements();
                this.loadImages();
                this.setupEventListeners();
            }
            
            initializeElements() {
                this.playBtn = document.getElementById('playBtn');
                this.pauseBtn = document.getElementById('pauseBtn');
                this.resetBtn = document.getElementById('resetBtn');
                this.speedRange = document.getElementById('speedRange');
                this.speedValue = document.getElementById('speedValue');
                this.frameRange = document.getElementById('frameRange');
                this.frameValue = document.getElementById('frameValue');
                this.statusText = document.getElementById('statusText');
                this.currentImage = document.getElementById('currentImage');
            }
            
            async loadImages() {
                const loadPromises = [];
                
                for (let i = 1; i <= this.totalFrames; i++) {
                    const img = new Image();
                    const promise = new Promise((resolve, reject) => {
                        img.onload = () => resolve(img);
                        img.onerror = () => reject(new Error(`Failed to load ${i}.png`));
                    });
                    img.src = `${i}.png`;
                    loadPromises.push(promise);
                    this.images.push(img);
                }
                
                try {
                    await Promise.all(loadPromises);
                    console.log('所有图片加载完成');
                    this.drawCurrentFrame();
                    this.updateStatus();
                } catch (error) {
                    console.error('图片加载失败:', error);
                    this.ctx.fillStyle = '#ff0000';
                    this.ctx.font = '20px Arial';
                    this.ctx.fillText('图片加载失败', 50, 150);
                }
            }
            
            drawCurrentFrame() {
                if (this.images[this.currentFrame] && this.images[this.currentFrame].complete) {
                    // 清空画布
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                    
                    const img = this.images[this.currentFrame];
                    
                    // 计算缩放比例以适应canvas
                    const scaleX = this.canvas.width / img.width;
                    const scaleY = this.canvas.height / img.height;
                    const scale = Math.min(scaleX, scaleY);
                    
                    const newWidth = img.width * scale;
                    const newHeight = img.height * scale;
                    const x = (this.canvas.width - newWidth) / 2;
                    const y = (this.canvas.height - newHeight) / 2;
                    
                    // 绘制图片
                    this.ctx.drawImage(img, x, y, newWidth, newHeight);
                }
            }
            
            nextFrame() {
                this.currentFrame = (this.currentFrame + 1) % this.totalFrames;
                this.drawCurrentFrame();
                this.updateStatus();
            }
            
            play() {
                if (!this.isPlaying) {
                    this.isPlaying = true;
                    this.animationInterval = setInterval(() => {
                        this.nextFrame();
                    }, this.frameDelay);
                    this.updateStatus();
                }
            }
            
            pause() {
                if (this.isPlaying) {
                    this.isPlaying = false;
                    clearInterval(this.animationInterval);
                    this.updateStatus();
                }
            }
            
            reset() {
                this.pause();
                this.currentFrame = 0;
                this.drawCurrentFrame();
                this.updateStatus();
            }
            
            setFrame(frameIndex) {
                this.currentFrame = frameIndex;
                this.drawCurrentFrame();
                this.updateStatus();
            }
            
            setSpeed(speed) {
                this.frameDelay = speed;
                if (this.isPlaying) {
                    // 重新启动动画以应用新速度
                    this.pause();
                    this.play();
                }
            }
            
            updateStatus() {
                this.statusText.textContent = this.isPlaying ? '播放中' : '已停止';
                this.currentImage.textContent = `${this.currentFrame + 1}.png`;
                this.frameValue.textContent = `${this.currentFrame + 1}/6`;
                this.frameRange.value = this.currentFrame + 1;
            }
            
            setupEventListeners() {
                this.playBtn.addEventListener('click', () => this.play());
                this.pauseBtn.addEventListener('click', () => this.pause());
                this.resetBtn.addEventListener('click', () => this.reset());
                
                this.speedRange.addEventListener('input', (e) => {
                    const speed = parseInt(e.target.value);
                    this.setSpeed(speed);
                    this.speedValue.textContent = `${speed}ms`;
                });
                
                this.frameRange.addEventListener('input', (e) => {
                    const frame = parseInt(e.target.value) - 1;
                    this.setFrame(frame);
                });
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            new CanvasImageSequence();
        });
    </script>
</body>
</html>
