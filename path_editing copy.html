<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #0a0e1a, #1e293b);
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .nav {
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(12px);
            border-bottom: 1px solid rgba(6, 182, 212, 0.2);
            padding: 16px 24px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 50;
        }
        
        .nav-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #06b6d4, #3b82f6);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .logo-text h1 {
            font-size: 20px;
            font-weight: bold;
            background: linear-gradient(135deg, #06b6d4, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .logo-text p {
            font-size: 12px;
            color: #9ca3af;
        }
        
        .nav-menu {
            display: flex;
            align-items: center;
            gap: 24px;
        }
        
        .nav-link {
            padding: 8px 16px;
            border-radius: 8px;
            color: #94a3b8;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            color: #06b6d4;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .nav-link.active {
            color: #06b6d4;
            background: rgba(6, 182, 212, 0.2);
            border: 1px solid rgba(6, 182, 212, 0.3);
        }
        
        .main {
            padding-top: 100px;
            padding: 100px 24px 24px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .toolbar {
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(12px);
            border-radius: 16px;
            border: 1px solid rgba(6, 182, 212, 0.2);
            padding: 16px;
            margin-bottom: 24px;
        }
        
        .toolbar-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 16px;
        }
        
        .toolbar-title {
            font-size: 18px;
            font-weight: 600;
            color: #06b6d4;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 24px;
        }
        
        .left-panel {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }
        
        .panel {
            background: rgba(30, 41, 59, 0.9);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(6, 182, 212, 0.3);
            backdrop-filter: blur(12px);
        }
        
        .panel-title {
            color: #06b6d4;
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .main-workspace {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(16px);
            border-radius: 16px;
            border: 1px solid rgba(6, 182, 212, 0.2);
            padding: 24px;
            min-height: 500px;
        }
        
        .workspace-title {
            font-size: 20px;
            font-weight: bold;
            color: white;
            margin-bottom: 16px;
        }
        
        .workspace-canvas {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #9ca3af;
        }
        
        @media (max-width: 1024px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-menu {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="nav">
        <div class="nav-content">
            <div class="logo">
                <div class="logo-icon">🌍</div>
                <div class="logo-text">
                    <h1>世界模型可视化展示大屏</h1>
                    <p>World Model Visualization Dashboard</p>
                </div>
            </div>
            
            <div class="nav-menu">
                <a href="#" class="nav-link active">仪表板</a>
                <a href="#" class="nav-link">分析</a>
                <a href="#" class="nav-link">设置</a>
                <a href="#" class="nav-link">帮助</a>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容 -->
    <main class="main">
        <!-- 工具栏 -->
        <div class="toolbar">
            <div class="toolbar-content">
                <div class="toolbar-title">
                    🛠️ 快捷操作
                </div>
                <div style="color: #9ca3af; font-size: 14px;">
                    系统状态: 正常运行
                </div>
            </div>
        </div>
        
        <!-- 内容网格 -->
        <div class="content-grid">
            <!-- 左侧面板 -->
            <div class="left-panel">
                <div class="panel">
                    <div class="panel-title">
                        📊 样本选择
                    </div>
                    <p style="color: #d1d5db; font-size: 14px; margin-bottom: 16px;">
                        选择不同的样本数据进行路径规划分析
                    </p>
                    <button style="width: 100%; padding: 12px; background: linear-gradient(135deg, #06b6d4, #3b82f6); color: white; border: none; border-radius: 8px; cursor: pointer;">
                        样本选择
                    </button>
                </div>
                
               
            </div>
            <!-- 主工作区 -->
            <div class="main-workspace">
                <div class="workspace-title">
                    🛣️ 路径编辑工作台
                </div>
                <div class="workspace-canvas">
                    3D 工作区域 - ThreeJS 渲染区
                </div>
            </div>
        </div>
    </main>
</body>
</html>
