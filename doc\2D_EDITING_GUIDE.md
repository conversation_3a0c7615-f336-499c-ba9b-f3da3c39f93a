# 2D路径编辑功能使用指南

## 功能概述

path_edit页面现在支持2D和3D两种可视化模式，用户可以在工具栏中切换视图模式，并在2D模式下进行路径点的编辑操作。

## 新增功能

### 1. 2D/3D视图切换

在工具栏左侧新增了视图模式切换标签页：

- **3D视图**: 使用ThreeJS渲染的3D场景，支持完整的x,y,z坐标显示
- **2D视图**: 使用Canvas渲染的2D场景，只显示x,y坐标和角度信息

### 2. 2D场景功能

#### 2D渲染特性
- 使用Canvas 2D API渲染路径和路径点
- 只显示X,Y坐标，忽略Z坐标
- 显示路径点的方向角度指示器
- 支持网格和坐标轴显示
- 可编辑点(flag=1)使用路径颜色，不可编辑点(flag=0)显示为灰色

#### 2D场景控制
- **缩放控制**: 右上角控制面板提供放大、缩小、重置缩放功能
- **鼠标位置显示**: 左下角状态面板实时显示鼠标在世界坐标系中的位置
- **选中点信息**: 显示当前选中路径点的ID

### 3. 2D点编辑功能

#### 点击编辑
1. 在2D视图模式下，点击任意路径点
2. 自动弹出"编辑2D路径点"模态框
3. 模态框显示点的详细信息：
   - X坐标和Y坐标输入框
   - 角度设置（数字输入框和滑块）
   - 点ID显示
   - 编辑状态选择

#### 编辑操作
- **坐标修改**: 直接在输入框中修改X,Y坐标值
- **角度调整**: 使用数字输入框或滑块调整方向角度（0-360度）
- **实时预览**: 角度调整时，预览区域会实时显示箭头方向
- **保存更改**: 点击"保存更改"按钮应用修改
- **删除点**: 点击"删除"按钮删除当前点（仅限可编辑点）

#### 实时更新
- 修改点坐标后，2D场景会立即更新显示
- 同时更新3D场景中的对应点位置
- 操作历史会记录所有编辑操作
- 统计信息会实时更新

## 使用流程

### 基本操作流程
1. 打开path_edit.html页面
2. 点击"样本选择"按钮选择数据样本
3. 在工具栏中点击"2D视图"标签切换到2D模式
4. 点击任意路径点打开编辑模态框
5. 修改坐标或角度参数
6. 点击"保存更改"应用修改
7. 可随时切换回3D视图查看完整的3D效果

### 注意事项
- 只有flag=1的点可以编辑，flag=0的点为锁定状态
- 2D模式下的编辑会同步到3D场景
- 删除操作需要确认，且不可撤销
- 坐标值支持小数，建议精度为0.1

## 技术实现

### 核心组件
- `canvas2DManager`: 2D Canvas渲染管理器
- `viewModeManager`: 视图模式切换管理器
- `showPoint2DEditModal()`: 2D点编辑模态框函数

### 数据同步
- 2D和3D场景共享同一份路径数据
- 修改操作会同时更新两个场景的显示
- 使用统一的数据结构确保一致性

### 性能优化
- 2D渲染使用Canvas原生API，性能优异
- 场景切换时只显示/隐藏对应容器，避免重复初始化
- 事件监听器合理管理，避免内存泄漏

## 扩展功能

未来可以考虑添加的功能：
- 2D场景中的拖拽编辑
- 批量点编辑
- 路径插值和平滑
- 2D场景的平移和旋转
- 导出2D路径图像
