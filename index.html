<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>世界模型可视化展示大屏</title>
    <meta name="description" content="现代化的路径编辑规划系统，提供直观的3D可视化界面和强大的路径编辑功能">
    <meta name="keywords" content="路径规划,3D可视化,世界模型,路径编辑,ThreeJS">
    <meta name="author" content="World Model Visualization Team">
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='40' fill='%2306b6d4'/><path d='M30 50 L50 30 L70 50 L50 70 Z' fill='white'/></svg>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 移除Tailwind CSS，使用自定义CSS -->
    <link rel="stylesheet" href="/static/css/styles.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="main-nav">
        <div class="nav-container">
            <div class="nav-content">
                <!-- Logo和标题 -->
                <div class="nav-brand">
                    <div class="nav-logo">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="nav-title">
                        <h1>世界模型可视化展示大屏</h1>
                        <p>World Model Visualization Dashboard</p>
                    </div>
                </div>

                <!-- 导航菜单 -->
                <div class="nav-menu">
                    <a href="#" class="nav-link active" data-section="dashboard">
                        <i class="fas fa-tachometer-alt"></i>仪表板
                    </a>
                    <a href="./path_edit.html" class="nav-link" >
                        <i class="fas fa-route"></i>路径编辑
                    </a>
                    <a href="#" class="nav-link" data-section="analysis">
                        <i class="fas fa-chart-line"></i>分析
                    </a>
                    <a href="#" class="nav-link" data-section="settings">
                        <i class="fas fa-cog"></i>设置
                    </a>
                    <div class="nav-dropdown">
                        <a href="#" class="nav-link" data-dropdown="help">
                            <i class="fas fa-question-circle"></i>帮助
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </a>
                        <div class="dropdown-menu" id="help-dropdown">
                            <a href="#" class="dropdown-item" data-action="guide">
                                <i class="fas fa-book-open"></i>操作指南
                            </a>
                            <a href="#" class="dropdown-item" data-action="shortcuts">
                                <i class="fas fa-keyboard"></i>快捷键
                            </a>
                            <a href="#" class="dropdown-item" data-action="about">
                                <i class="fas fa-info-circle"></i>关于系统
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 右侧工具栏 -->
                <div class="nav-tools">
                    <!-- 状态指示器 -->
                    <div class="status-indicator">
                        <div class="status-dot"></div>
                        <span>系统正常</span>
                    </div>

                    <!-- 通知按钮 -->
                    <button class="nav-tool-btn">
                        <i class="fas fa-bell"></i>
                      
                    </button>

                    <!-- 全屏按钮 -->
                    <button id="fullscreen-toggle" class="nav-tool-btn">
                        <i class="fas fa-expand"></i>
                    </button>

                    <!-- 移动端菜单按钮 -->
                    <button id="mobile-menu-toggle" class="nav-tool-btn mobile-only">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

       
    </nav>

    <!-- 主要内容区域 -->
    <main style="padding-top: 120px; padding-bottom: 24px; padding-left: 24px; padding-right: 24px; min-height: 100vh;">
        <div style="max-width: 1400px; margin: 0 auto;">
         

            <!-- 主要内容网格 -->
            <div class="main-grid">
                <!-- 左侧控制面板 -->
                <div class="left-panel">
                    <!-- 样本选择面板 -->
                    <div class="panel-container">
                        <div class="panel-header">
                            <h2 class="panel-title">
                                <i class="fas fa-database"></i>
                                飞行样本选择
                            </h2>
                            <span class="status-badge status-active">活跃</span>
                        </div>

                        <div class="panel-content">
                            <!-- <div class="info-card">
                                <i class="fas fa-info-circle"></i>
                                <span>选择不同的样本数据进行路径规划分析</span>
                            </div> -->

                            <div class="button-group">
                                <button id="sample-button" class="tech-button-primary">
                                    <i class="fas fa-database"></i>
                                    飞行样本选择
                                    <i class="fas fa-chevron-right"></i>
                                </button>

                                <!-- <button id="fetch-points-button" class="tech-button-secondary">
                                    <i class="fas fa-download"></i>
                                    获取路径点
                                    <i class="fas fa-arrow-down"></i>
                                </button> -->
                            </div>

                            <!-- 样本统计信息 -->
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-value">12</div>
                                    <div class="stat-label">可用样本</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-value">3</div>
                                    <div class="stat-label">已选择</div>
                                </div>
                            </div>
                        </div>

                        <!-- 路径点容器 -->
                        <div class="points-section">
                            <div class="points-header">
                                <h3 class="points-title">
                                    <i class="fas fa-route"></i>飞行轨迹列表
                                </h3>
                                <span class="points-count" id="points-count">0 个点</span>
                            </div>
                            <div id="points-container" class="points-container"></div>
                        </div>
                    </div>

             

                </div>

                <!-- 主要工作区域 -->
                <div class="main-workspace">
                    <!-- 路径编辑主面板 -->
                    <div class="workspace-panel">
                        <div class="workspace-header">
                            <div class="workspace-title-section">
                                <div class="workspace-icon">
                                    <i class="fas fa-route"></i>
                                </div>
                                <div class="workspace-title-text">
                                    <h2 class="workspace-title">飞行轨迹工作台</h2>
                                    <p class="workspace-subtitle">Path Planning & Editing Workspace</p>
                                </div>
                          
                                <!-- 工具栏 -->
                                <div class="edit-toolbar">
                                    <!-- 模式指示器 -->
                                    <div class="mode-indicator-container">
                                        <div class="mode-indicator" id="mode-indicator">
                                            <div class="mode-icon">
                                                <i class="fas fa-arrows-alt"></i>
                                            </div>
                                            <div class="mode-info">
                                                <span class="mode-name">移动模式</span>
                                                <span class="mode-shortcut">按 M 切换</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="toolbar-separator"></div>

                                    <!-- 编辑工具 -->

                                    <div class="toolbar-separator"></div>

                                    <!-- 路径样式工具 -->
                                    <div class="toolbar-group">
                                        <div class="path-thickness-control">
                                            <label class="thickness-label">
                                                <i class="fas fa-minus"></i>
                                                <input type="range" id="path-thickness-slider" min="1" max="10" value="3" step="0.5" class="thickness-slider">
                                                <i class="fas fa-plus"></i>
                                            </label>
                                            <span class="thickness-value" id="thickness-value">3.0</span>
                                        </div>
                                    </div>

                                    <div class="toolbar-separator"></div>

                                    <!-- 视图模式切换 -->
                                    <div class="toolbar-group">
                                        <div class="view-mode-toggle">
                                            <button class="toolbar-btn view-mode-btn " data-mode="3d" title="3D视图" onclick="switchViewMode('3d')" style="display: none;">
                                                <i class="fas fa-cube"></i>
                                                <span>3D</span>
                                            </button>
                                            <button class="toolbar-btn view-mode-btn active" data-mode="2d" title="2D视图" onclick="switchViewMode('2d')">
                                                <i class="fas fa-map"></i>
                                                <span>2D</span>
                                            </button>
                                        </div>
                                    </div>

                                    <div class="toolbar-separator"></div>

                                    <!-- 视图工具 -->
                                    <div class="toolbar-group">
                                        <button class="toolbar-btn" data-tool="zoom-in" title="放大" onclick="zoomIn()">
                                            <i class="fas fa-search-plus"></i>
                                        </button>
                                        <button class="toolbar-btn" data-tool="zoom-out" title="缩小" onclick="zoomOut()">
                                            <i class="fas fa-search-minus"></i>
                                        </button>
                                    
                                    </div>
                                </div>

                             

                                <!-- 全屏按钮 -->
                                <button class="panel-action-btn fullscreen-btn" data-target="processed-video-container" title="全屏">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 状态栏 -->
                        <div class="workspace-status">
                            <div class="status-left">
                                <!-- 图例按钮已删除 -->

                                <span class="status-item">
                                    <i class="fas fa-mouse-pointer"></i>
                                    <span id="current-mode">移动模式</span>
                                </span>
                                <span class="status-item">
                                    <i class="fas fa-crosshairs"></i>
                                    <span id="cursor-coords">坐标: (0, 0)</span>
                                </span>
                                <span class="status-item">
                                    <i class="fas fa-search"></i>
                                    <span id="zoom-level">缩放: 100%</span>
                                </span>
                            </div>
                            <div class="status-right">
                                <div class="status-item layer-selector">
                                    <i class="fas fa-layer-group"></i>
                                    <span>飞行轨迹:</span>
                                    <select id="layer-select" class="layer-select-dropdown">
                                        <option value="path-1" data-color="#06b6d4">主路径</option>
                                        <option value="path-2" data-color="#3b82f6">备用路径</option>
                                        <option value="path-3" data-color="#10b981">优化路径</option>
                                        <option value="all" data-color="#ffffff">显示全部</option>
                                    </select>
                                    <div class="layer-color-indicator" id="layer-color"></div>
                                </div>
                                <span class="status-item">
                                    <i class="fas fa-save"></i>
                                    已保存
                                </span>
                            </div>
                        </div>

                        <!-- 工作区域 -->
                        <div class="workspace-canvas-container">
                              <!-- 2D视图容器 -->
                            <div id="canvas-2d-container" class="workspace-canvas view-2d">
                                <div class="scene-placeholder">
                                    <div class="scene-content">
                                        <div class="scene-icon">
                                            <i class="fas fa-map"></i>
                                        </div>
                                        <h3>2D路径编辑器</h3>
                                        <p>Canvas 2D场景将在此处渲染</p>
                                        <div class="scene-features">
                                            <div class="feature-item">
                                                <i class="fas fa-mouse-pointer"></i>
                                                <span>平面编辑</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-route"></i>
                                                <span>路径规划</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-eye"></i>
                                                <span>俯视图</span>
                                            </div>
                                        </div>
                                        <div class="loading-indicator">
                                            <div class="loading-spinner"></div>
                                            <span>正在初始化2D引擎...</span>
                                        </div>
                                    </div>
                                </div>
                                <!-- 2D Canvas -->
                                <canvas id="canvas-2d" style="display: none;"></canvas>
                            </div>

                            <!-- 3D视图容器 -->
                            <div id="processed-video-container" class="workspace-canvas view-3d active" style="display: none;" >                              
                                <div class="scene-placeholder">
                                    <div class="scene-content">
                                        <div class="scene-icon">
                                            <i class="fas fa-cube"></i>
                                        </div>
                                        <h3>3D路径编辑器</h3>
                                        <p>ThreeJS 3D场景将在此处渲染</p>
                                        <div class="scene-features">
                                            <div class="feature-item">
                                                <i class="fas fa-mouse-pointer"></i>
                                                <span>交互式编辑</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-route"></i>
                                                <span>路径规划</span>
                                            </div>
                                            <div class="feature-item">
                                                <i class="fas fa-eye"></i>
                                                <span>实时预览</span>
                                            </div>
                                        </div>
                                        <div class="loading-indicator">
                                            <div class="loading-spinner"></div>
                                            <span>正在初始化3D引擎...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        
                        </div>

                    </div>

                    <!-- 底部信息面板 -->
                    <div class="bottom-panels">
                        <!-- 路径统计 -->
                        <div class="info-panel-card">
                            <div class="info-panel-header">
                                <i class="fas fa-chart-bar"></i>
                                <h3>路径统计</h3>
                                <div class="panel-badge">实时</div>
                            </div>
                            <div class="info-panel-content">
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </div>
                                        <div class="stat-data">
                                            <span class="stat-value">24</span>
                                            <span class="stat-label">路径点</span>
                                        </div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <i class="fas fa-ruler"></i>
                                        </div>
                                        <div class="stat-data">
                                            <span class="stat-value">156.7m</span>
                                            <span class="stat-label">总长度</span>
                                        </div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <i class="fas fa-compass"></i>
                                        </div>
                                        <div class="stat-data">
                                            <span class="stat-value">12.3°</span>
                                            <span class="stat-label">平均角度</span>
                                        </div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div class="stat-data">
                                            <span class="stat-value">2.5s</span>
                                            <span class="stat-label">时间</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 性能监控 -->
                        <div class="info-panel-card">
                            <div class="info-panel-header">
                                <i class="fas fa-tachometer-alt"></i>
                                <h3>性能监控</h3>
                                <div class="panel-badge success">正常</div>
                            </div>
                            <div class="info-panel-content">
                                <div class="performance-metrics">
                                    <div class="metric">
                                        <div class="metric-header">
                                            <span class="metric-label">渲染FPS</span>
                                            <span class="metric-value">60</span>
                                        </div>
                                        <div class="metric-bar">
                                            <div class="metric-fill success" style="width: 90%"></div>
                                        </div>
                                    </div>
                                    <div class="metric">
                                        <div class="metric-header">
                                            <span class="metric-label">内存使用</span>
                                            <span class="metric-value">45%</span>
                                        </div>
                                        <div class="metric-bar">
                                            <div class="metric-fill warning" style="width: 45%"></div>
                                        </div>
                                    </div>
                                    <div class="metric">
                                        <div class="metric-header">
                                            <span class="metric-label">GPU使用</span>
                                            <span class="metric-value">32%</span>
                                        </div>
                                        <div class="metric-bar">
                                            <div class="metric-fill success" style="width: 32%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 最近操作 -->
                        <div class="info-panel-card">
                            <div class="info-panel-header">
                                <i class="fas fa-history"></i>
                                <h3>操作历史</h3>
                                <button class="clear-history-btn" title="清空历史">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                            <div class="info-panel-content">
                                <div class="activity-list">
                                    <div class="activity-item">
                                        <div class="activity-icon success">
                                            <i class="fas fa-plus"></i>
                                        </div>
                                        <div class="activity-content">
                                            <span class="activity-text">添加路径点 #24</span>
                                            <span class="activity-time">2分钟前</span>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <div class="activity-icon warning">
                                            <i class="fas fa-edit"></i>
                                        </div>
                                        <div class="activity-content">
                                            <span class="activity-text">修改点 #23 角度</span>
                                            <span class="activity-time">5分钟前</span>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <div class="activity-icon info">
                                            <i class="fas fa-save"></i>
                                        </div>
                                        <div class="activity-content">
                                            <span class="activity-text">保存项目配置</span>
                                            <span class="activity-time">10分钟前</span>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <div class="activity-icon info">
                                            <i class="fas fa-upload"></i>
                                        </div>
                                        <div class="activity-content">
                                            <span class="activity-text">导入样本数据</span>
                                            <span class="activity-time">15分钟前</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Sample selection modal -->
    <div id="sample-modal" class="modal">
        <div class="modal-content-enhanced">
            <div class="modal-header">
                <div class="flex items-center space-x-3">
                    <div class="modal-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div>
                        <h2 class="modal-title">样本数据选择</h2>
                        <p class="modal-subtitle">请选择用于路径规划的样本数据</p>
                    </div>
                </div>
                <button class="close-button-enhanced">&times;</button>
            </div>

            <div class="modal-body">
                <div class="sample-grid">
                    <div class="sample-card" data-sample="1">
                        <div class="sample-preview">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="sample-info">
                            <h3>样本数据 1</h3>
                            <p>城市道路场景</p>
                            <div class="sample-stats">
                                <span><i class="fas fa-route"></i> 125 点</span>
                                <span><i class="fas fa-clock"></i> 2.3km</span>
                            </div>
                        </div>
                        <div class="sample-status">
                            <span class="status-badge status-active">可用</span>
                        </div>
                    </div>

                    <div class="sample-card" data-sample="2">
                        <div class="sample-preview">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <div class="sample-info">
                            <h3>样本数据 2</h3>
                            <p>山区道路场景</p>
                            <div class="sample-stats">
                                <span><i class="fas fa-route"></i> 89 点</span>
                                <span><i class="fas fa-clock"></i> 1.8km</span>
                            </div>
                        </div>
                        <div class="sample-status">
                            <span class="status-badge status-active">可用</span>
                        </div>
                    </div>

                    <div class="sample-card" data-sample="3">
                        <div class="sample-preview">
                            <i class="fas fa-road"></i>
                        </div>
                        <div class="sample-info">
                            <h3>样本数据 3</h3>
                            <p>高速公路场景</p>
                            <div class="sample-stats">
                                <span><i class="fas fa-route"></i> 67 点</span>
                                <span><i class="fas fa-clock"></i> 3.1km</span>
                            </div>
                        </div>
                        <div class="sample-status">
                            <span class="status-badge status-active">可用</span>
                        </div>
                    </div>

                    <div class="sample-card" data-sample="4">
                        <div class="sample-preview">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="sample-info">
                            <h3>样本数据 4</h3>
                            <p>商业区场景</p>
                            <div class="sample-stats">
                                <span><i class="fas fa-route"></i> 156 点</span>
                                <span><i class="fas fa-clock"></i> 2.7km</span>
                            </div>
                        </div>
                        <div class="sample-status">
                            <span class="status-badge status-paused">维护中</span>
                        </div>
                    </div>

                    <div class="sample-card" data-sample="5">
                        <div class="sample-preview">
                            <i class="fas fa-tree"></i>
                        </div>
                        <div class="sample-info">
                            <h3>样本数据 5</h3>
                            <p>乡村道路场景</p>
                            <div class="sample-stats">
                                <span><i class="fas fa-route"></i> 98 点</span>
                                <span><i class="fas fa-clock"></i> 1.9km</span>
                            </div>
                        </div>
                        <div class="sample-status">
                            <span class="status-badge status-active">可用</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button class="btn-secondary" onclick="document.getElementById('sample-modal').style.display='none'">
                    <i class="fas fa-times mr-2"></i>取消
                </button>
                <button class="btn-primary" id="confirm-sample">
                    <i class="fas fa-check mr-2"></i>确认选择
                </button>
            </div>
        </div>
    </div>

    <!-- Point edit modal -->
    <div id="point-edit-modal" class="modal">
        <div class="modal-content-enhanced">
            <div class="modal-header">
                <div class="flex items-center space-x-3">
                    <div class="modal-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div>
                        <h2 class="modal-title">编辑路径点</h2>
                        <p class="modal-subtitle">调整路径点的位置和方向参数</p>
                    </div>
                </div>
                <button class="close-button-enhanced">&times;</button>
            </div>

            <div class="modal-body">
                <form id="point-edit-form" class="space-y-6">
                    <!-- 坐标设置 -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-crosshairs mr-2"></i>坐标设置
                        </h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group-enhanced">
                                <label for="point-x" class="form-label">
                                    <i class="fas fa-arrows-alt-h mr-2"></i>X坐标
                                </label>
                                <input type="number" id="point-x" name="x" step="0.1" class="form-input-enhanced" placeholder="0.0">
                                <div class="form-help">水平位置坐标</div>
                            </div>
                            <div class="form-group-enhanced">
                                <label for="point-y" class="form-label">
                                    <i class="fas fa-arrows-alt-v mr-2"></i>Y坐标
                                </label>
                                <input type="number" id="point-y" name="y" step="0.1" class="form-input-enhanced" placeholder="0.0">
                                <div class="form-help">垂直位置坐标</div>
                            </div>
                        </div>
                    </div>

                    <!-- 角度设置 -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-sync mr-2"></i>方向角度
                        </h3>
                        <div class="form-group-enhanced">
                            <label for="point-angle" class="form-label">
                                角度 (°)
                            </label>
                            <div class="angle-input-group">
                                <input type="number" id="point-angle" name="angle" min="0" max="360" step="1" class="form-input-enhanced angle-input" placeholder="0">
                                <div class="angle-slider-wrapper">
                                    <input type="range" id="angle-slider" min="0" max="360" class="angle-slider-enhanced">
                                    <div class="angle-marks">
                                        <span>0°</span>
                                        <span>90°</span>
                                        <span>180°</span>
                                        <span>270°</span>
                                        <span>360°</span>
                                    </div>
                                </div>
                                <div class="angle-preview">
                                    <div class="angle-indicator">
                                        <div class="angle-arrow"></div>
                                    </div>
                                    <div class="angle-value">0°</div>
                                </div>
                            </div>
                            <div class="form-help">设置路径点的朝向角度</div>
                        </div>
                    </div>

                    <!-- 高级设置 -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-cog mr-2"></i>高级设置
                        </h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group-enhanced">
                                <label class="form-label">点类型</label>
                                <select class="form-input-enhanced">
                                    <option value="normal">普通点</option>
                                    <option value="waypoint">路径点</option>
                                    <option value="checkpoint">检查点</option>
                                </select>
                            </div>
                            <div class="form-group-enhanced">
                                <label class="form-label">优先级</label>
                                <select class="form-input-enhanced">
                                    <option value="low">低</option>
                                    <option value="normal" selected>普通</option>
                                    <option value="high">高</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="document.getElementById('point-edit-modal').style.display='none'">
                    <i class="fas fa-times mr-2"></i>取消
                </button>
                <button type="button" class="btn-danger" id="delete-point">
                    <i class="fas fa-trash mr-2"></i>删除
                </button>
                <button type="submit" form="point-edit-form" class="btn-primary">
                    <i class="fas fa-save mr-2"></i>保存更改
                </button>
            </div>
        </div>
    </div>

    <!-- 角度调整控制器 -->
    <div id="angle-control" class="angle-control">
        <div class="angle-control-line"></div>
        <div class="angle-control-handle"></div>
    </div>

    <!-- 操作指南模态框 -->
    <div id="operation-guide-modal" class="modal">
        <div class="modal-content-enhanced">
            <div class="modal-header">
                <div class="modal-icon">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div>
                    <h2 class="modal-title">操作指南</h2>
                    <p class="modal-subtitle">路径编辑器使用说明</p>
                </div>
                <button class="close-button-enhanced" onclick="closeOperationGuide()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="guide-sections">
                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-mouse"></i>鼠标操作
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <kbd>左键单击</kbd>
                                <span>选择路径点</span>
                            </div>
                            <div class="guide-item">
                                <kbd>左键拖拽</kbd>
                                <span>移动选中的路径点</span>
                            </div>
                            <div class="guide-item">
                                <kbd>右键单击</kbd>
                                <span>旋转路径点方向</span>
                            </div>
                            <div class="guide-item">
                                <kbd>双击</kbd>
                                <span>编辑路径点属性</span>
                            </div>
                            <div class="guide-item">
                                <kbd>滚轮</kbd>
                                <span>缩放3D视图</span>
                            </div>
                        </div>
                    </div>

                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-keyboard"></i>键盘快捷键
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <kbd>M</kbd>
                                <span>切换移动/旋转模式</span>
                            </div>
                            <div class="guide-item">
                                <kbd>A</kbd>
                                <span>添加新路径点</span>
                            </div>
                            <div class="guide-item">
                                <kbd>Delete</kbd>
                                <span>删除选中的路径点</span>
                            </div>
                            <div class="guide-item">
                                <kbd>Ctrl + S</kbd>
                                <span>保存当前项目</span>
                            </div>
                            <div class="guide-item">
                                <kbd>Ctrl + Z</kbd>
                                <span>撤销上一步操作</span>
                            </div>
                            <div class="guide-item">
                                <kbd>Ctrl + Y</kbd>
                                <span>重做操作</span>
                            </div>
                        </div>
                    </div>

                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-route"></i>路径管理
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <span class="guide-color" style="background: #06b6d4;"></span>
                                <span>主路径 - 默认规划路径</span>
                            </div>
                            <div class="guide-item">
                                <span class="guide-color" style="background: #3b82f6;"></span>
                                <span>备用路径 - 备选方案</span>
                            </div>
                            <div class="guide-item">
                                <span class="guide-color" style="background: #10b981;"></span>
                                <span>优化路径 - 优化后的路径</span>
                            </div>
                        </div>
                    </div>

                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-cog"></i>高级功能
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <kbd>Shift + 左键</kbd>
                                <span>多选路径点</span>
                            </div>
                            <div class="guide-item">
                                <kbd>Ctrl + 左键</kbd>
                                <span>添加到选择</span>
                            </div>
                            <div class="guide-item">
                                <kbd>Alt + 拖拽</kbd>
                                <span>复制路径点</span>
                            </div>
                            <div class="guide-item">
                                <kbd>Tab</kbd>
                                <span>切换路径显示</span>
                            </div>
                            <div class="guide-item">
                                <kbd>Space</kbd>
                                <span>播放/暂停动画</span>
                            </div>
                            <div class="guide-item">
                                <kbd>R</kbd>
                                <span>重置视图</span>
                            </div>
                        </div>
                    </div>

                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-tools"></i>工具栏功能
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <span class="guide-icon"><i class="fas fa-plus"></i></span>
                                <span>添加新路径点到当前位置</span>
                            </div>
                            <div class="guide-item">
                                <span class="guide-icon"><i class="fas fa-trash"></i></span>
                                <span>删除选中的路径点</span>
                            </div>
                            <div class="guide-item">
                                <span class="guide-icon"><i class="fas fa-save"></i></span>
                                <span>保存当前路径配置</span>
                            </div>
                            <div class="guide-item">
                                <span class="guide-icon"><i class="fas fa-undo"></i></span>
                                <span>撤销上一步操作</span>
                            </div>
                            <div class="guide-item">
                                <span class="guide-icon"><i class="fas fa-redo"></i></span>
                                <span>重做操作</span>
                            </div>
                            <div class="guide-item">
                                <span class="guide-icon"><i class="fas fa-eye"></i></span>
                                <span>切换路径可见性</span>
                            </div>
                        </div>
                    </div>

                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-video"></i>视频生成
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <span class="guide-icon"><i class="fas fa-play"></i></span>
                                <span>生成原始视频预览</span>
                            </div>
                            <div class="guide-item">
                                <span class="guide-icon"><i class="fas fa-magic"></i></span>
                                <span>生成AI预测视频</span>
                            </div>
                            <div class="guide-item">
                                <span class="guide-icon"><i class="fas fa-download"></i></span>
                                <span>下载生成的视频文件</span>
                            </div>
                            <div class="guide-item">
                                <span class="guide-icon"><i class="fas fa-share"></i></span>
                                <span>分享视频链接</span>
                            </div>
                        </div>
                    </div>

                    <div class="guide-section">
                        <h3 class="guide-section-title">
                            <i class="fas fa-lightbulb"></i>使用技巧
                        </h3>
                        <div class="guide-items">
                            <div class="guide-item">
                                <span class="guide-tip">💡</span>
                                <span>使用右键菜单快速访问常用功能</span>
                            </div>
                            <div class="guide-item">
                                <span class="guide-tip">💡</span>
                                <span>按住Shift键可以精确调整路径点位置</span>
                            </div>
                            <div class="guide-item">
                                <span class="guide-tip">💡</span>
                                <span>双击路径点可以快速编辑其属性</span>
                            </div>
                            <div class="guide-item">
                                <span class="guide-tip">💡</span>
                                <span>使用滚轮可以快速缩放3D视图</span>
                            </div>
                            <div class="guide-item">
                                <span class="guide-tip">💡</span>
                                <span>保存项目前建议先预览生成的视频</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeOperationGuide()">
                    <i class="fas fa-times"></i>关闭
                </button>
                <button type="button" class="btn-primary" onclick="showShortcuts()">
                    <i class="fas fa-bolt"></i>查看快捷键
                </button>
            </div>
        </div>
    </div>

    <!-- Success notification -->
    <div id="success-notification" class="notification">
        <div class="notification-content">
            <p><i class="fas fa-check-circle"></i> 操作成功完成！</p>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <!-- Three.js OrbitControls -->
    <script src="/static/js/OrbitControls.js"></script>
    <script src="/static/js/script.js"></script>
</body>
</html> 