:root {
  --primary-color: #0a0e1a;
  --primary-light: #1e293b;
  --primary-dark: #020617;
  --secondary-color: #3b82f6;
  --secondary-light: #60a5fa;
  --secondary-dark: #1d4ed8;
  --accent-color: #06b6d4;
  --accent-light: #22d3ee;
  --accent-dark: #0891b2;
  --text-color: #f1f5f9;
  --text-muted: #94a3b8;
  --panel-bg: rgba(30, 41, 59, 0.95);
  --panel-bg-light: rgba(51, 65, 85, 0.8);
  --modal-bg: rgba(15, 23, 42, 0.98);
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --panel-border: rgba(6, 182, 212, 0.3);
  --panel-border-hover: rgba(6, 182, 212, 0.5);
  --button-shadow: rgba(6, 182, 212, 0.4);
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background: var(--primary-color);
  color: var(--text-color);
  background-image:
    radial-gradient(circle at 10% 20%, rgba(6, 182, 212, 0.12) 0%, transparent 30%),
    radial-gradient(circle at 90% 80%, rgba(59, 130, 246, 0.12) 0%, transparent 30%),
    radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.08) 0%, transparent 40%),
    linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  background-attachment: fixed;
  min-height: 100vh;
  overflow-x: hidden;
}

/* 主要内容区域 */
.main-content {
  margin-top: 80px; /* 为固定导航栏留出空间 */
  min-height: calc(100vh - 80px);
  padding: 0;
}

.main-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  width: 100%;
}

/* 导航栏样式 */
.main-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(6, 182, 212, 0.2);
}

.nav-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 16px;
}

.nav-logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
}

.nav-title h1 {
  font-size: 24px;
  font-weight: bold;
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.nav-title p {
  font-size: 14px;
  color: #9ca3af;
  margin: 4px 0 0 0;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 24px;
}

.nav-link {
  padding: 8px 16px;
  border-radius: 8px;
  color: #94a3b8;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-link:hover {
  color: var(--accent-color);
  background: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
  color: var(--accent-color);
  background: rgba(6, 182, 212, 0.2);
  border: 1px solid rgba(6, 182, 212, 0.3);
}

.nav-tools {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #d1d5db;
}

.status-dot {
  width: 12px;
  height: 12px;
  background: var(--success-color);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.nav-tool-btn {
  position: relative;
  padding: 8px;
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.nav-tool-btn:hover {
  color: var(--accent-color);
  background: rgba(255, 255, 255, 0.1);
}

.notification-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 8px;
  height: 8px;
  background: var(--danger-color);
  border-radius: 50%;
  height: 60px;
}

.mobile-only {
  display: none;
}

.mobile-menu {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(12px);
  border-top: 1px solid rgba(6, 182, 212, 0.2);
}

.mobile-menu-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 16px 24px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  color: #94a3b8;
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.mobile-nav-link:hover {
  color: var(--accent-color);
  background: rgba(255, 255, 255, 0.1);
}

/* 导航下拉菜单 */
.nav-dropdown {
  position: relative;
}

.dropdown-arrow {
  margin-left: 4px;
  font-size: 10px;
  transition: transform 0.3s ease;
}

.nav-dropdown:hover .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(16px);
  border-radius: 8px;
  border: 1px solid rgba(6, 182, 212, 0.3);
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 100;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.nav-dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  color: #d1d5db;
  text-decoration: none;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background: rgba(6, 182, 212, 0.1);
  color: var(--accent-color);
}

.dropdown-item i {
  width: 16px;
  text-align: center;
  color: var(--accent-color);
}

/* 图层选择器 */
.layer-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.layer-select-dropdown {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(6, 182, 212, 0.3);
  border-radius: 4px;
  color: white;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.layer-select-dropdown:hover {
  border-color: rgba(6, 182, 212, 0.5);
}

.layer-select-dropdown:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2);
}

.layer-color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--accent-color);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

/* 路径图例 */
.path-legend {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(12px);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid rgba(6, 182, 212, 0.2);
  max-width: 250px;
}

.legend-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--accent-color);
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #d1d5db;
}

.legend-color {
  width: 16px;
  height: 3px;
  border-radius: 2px;
  flex-shrink: 0;
}

.legend-points {
  font-size: 10px;
  color: #9ca3af;
  margin-left: auto;
}

.legend-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.legend-name {
  font-weight: 500;
  color: #f1f5f9;
}

.legend-status {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.legend-status.status-original {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.legend-status.status-predicted {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.legend-status.status-unknown {
  background: rgba(156, 163, 175, 0.2);
  color: #9ca3af;
}

.legend-actions {
  display: flex;
  gap: 4px;
  margin-left: auto;
}

.legend-toggle,
.legend-play,
.legend-generate,
.legend-video {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: #94a3b8;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

.legend-toggle:hover,
.legend-play:hover,
.legend-generate:hover,
.legend-video:hover {
  background: rgba(6, 182, 212, 0.2);
  color: var(--accent-color);
  transform: scale(1.1);
}

.legend-toggle.active {
  background: rgba(6, 182, 212, 0.3);
  color: var(--accent-color);
}

.legend-play {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.legend-play:hover {
  background: rgba(16, 185, 129, 0.3);
  color: #10b981;
}

.legend-generate {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.legend-generate:hover {
  background: rgba(59, 130, 246, 0.3);
  color: #3b82f6;
}

.legend-video {
  background: rgba(139, 92, 246, 0.2);
  color: #8b5cf6;
}

.legend-video:hover {
  background: rgba(139, 92, 246, 0.3);
  color: #8b5cf6;
}

.legend-generate:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.legend-item.hidden {
  opacity: 0.5;
}

.legend-item.hidden .legend-name {
  text-decoration: line-through;
}

/* 现代化视频对比模态框样式 */
.video-comparison-modal .modal-content-enhanced {
  max-width: 1400px;
  width: 95vw;
  max-height: 95vh;
  border-radius: 20px;
  overflow: hidden;
}

.video-comparison-content {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.98), rgba(30, 41, 59, 0.95));
  backdrop-filter: blur(25px);
  border: 1px solid rgba(6, 182, 212, 0.2);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 现代化头部样式 */
.modal-header-modern {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(59, 130, 246, 0.1));
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.modal-icon-modern {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  box-shadow: 0 8px 16px rgba(6, 182, 212, 0.3);
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.modal-title-modern {
  font-size: 24px;
  font-weight: 700;
  color: #f1f5f9;
  margin: 0;
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modal-subtitle-modern {
  font-size: 14px;
  color: #94a3b8;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.video-stats {
  display: flex;
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  font-size: 12px;
  color: #e2e8f0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item i {
  color: var(--accent-color);
}

.close-btn-modern {
  width: 40px;
  height: 40px;
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 10px;
  color: #ef4444;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.close-btn-modern:hover {
  background: rgba(239, 68, 68, 0.3);
  transform: scale(1.05);
}

/* 现代化主体样式 */
.modal-body-modern {
  padding: 24px 32px;
  max-height: 70vh;
  overflow-y: auto;
  background: rgba(0, 0, 0, 0.1);
}

.video-comparison-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

/* 视频卡片样式 */
.video-card {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8), rgba(30, 41, 59, 0.6));
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.video-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.original-card {
  border-left: 4px solid #10b981;
}

.predicted-card {
  border-left: 4px solid #3b82f6;
  height: 400px;
}

.video-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.video-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #f1f5f9;
}

.video-title i {
  font-size: 18px;
  color: var(--accent-color);
}

.video-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.3));
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.badge-pending {
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.2), rgba(107, 114, 128, 0.3));
  color: #9ca3af;
  border: 1px solid rgba(156, 163, 175, 0.3);
}

.badge-error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.3));
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* 视频卡片主体 */
.video-card-body {
  padding: 0;
  position: relative;
  height: 280px; /* 设置固定高度 */
  min-height: 280px;
}

.video-wrapper {
  position: relative;
  background: #000;
  border-radius: 0;
  overflow: hidden;
  width: 100%;
  height: 100%; /* 填满父容器 */
}

.video-player {
  width: 100%;
  height: 100%; /* 填满video-wrapper */
  object-fit: cover;
  display: block;
  background: #000;
}

.video-overlay {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-wrapper:hover .video-overlay {
  opacity: 1;
}

.video-control-btn {
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.video-control-btn:hover {
  background: rgba(6, 182, 212, 0.8);
  transform: scale(1.1);
}

.video-placeholder {
  height: 100%; /* 填满video-card-body */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.5), rgba(30, 41, 59, 0.3));
  color: #94a3b8;
  text-align: center;
  padding: 40px 20px;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
  color: #64748b;
}

.placeholder-text h4 {
  font-size: 18px;
  color: #e2e8f0;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.placeholder-text p {
  font-size: 14px;
  color: #94a3b8;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.generate-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  border-radius: 10px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.generate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb, #1e40af);
}

.generate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.generate-btn i {
  font-size: 16px;
}

/* 视频卡片底部 */
.video-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px 20px;
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.video-info {
  display: flex;
  gap: 16px;
  flex: 1;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #94a3b8;
}

.info-item i {
  color: var(--accent-color);
  font-size: 14px;
}

.download-btn {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.3));
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 8px;
  color: #10b981;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.download-btn:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(5, 150, 105, 0.4));
  transform: scale(1.1);
}

/* 现代化底部样式 */
.modal-footer-modern {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 32px 24px;
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-left,
.footer-right {
  display: flex;
  gap: 12px;
}

.btn-modern {
  padding: 12px 20px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  text-decoration: none;
}

.btn-modern.btn-primary {
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  color: white;
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
}

.btn-modern.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(6, 182, 212, 0.4);
}

.btn-modern.btn-secondary {
  background: rgba(71, 85, 105, 0.3);
  color: #e2e8f0;
  border: 1px solid rgba(71, 85, 105, 0.5);
}

.btn-modern.btn-secondary:hover {
  background: rgba(71, 85, 105, 0.5);
  transform: translateY(-1px);
}

.btn-modern.btn-outline {
  background: transparent;
  color: var(--accent-color);
  border: 1px solid rgba(6, 182, 212, 0.5);
}

.btn-modern.btn-outline:hover {
  background: rgba(6, 182, 212, 0.1);
  border-color: var(--accent-color);
}

/* 全屏视频模态框 */
.fullscreen-video-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.fullscreen-video-container {
  position: relative;
  width: 90%;
  height: 90%;
  max-width: 1200px;
}

.fullscreen-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 12px;
}

.fullscreen-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 48px;
  height: 48px;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 12px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  backdrop-filter: blur(10px);
}

.fullscreen-close-btn:hover {
  background: rgba(239, 68, 68, 0.8);
  transform: scale(1.1);
}

.video-header h3,
.video-header h4 {
  color: #f1f5f9;
  margin: 0;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.video-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.video-status.original {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.video-status.generated {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.video-status.not-generated {
  background: rgba(156, 163, 175, 0.2);
  color: #9ca3af;
}

.video-container {
  position: relative;
  width: 100%;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
  min-height: 200px;
}

.video-player {
  width: 100%;
  height: auto;
  max-height: 300px;
  display: block;
}

.video-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: rgba(0, 0, 0, 0.5);
  color: #94a3b8;
}

.placeholder-content {
  text-align: center;
}

.placeholder-content i {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.placeholder-content p {
  margin: 8px 0 16px 0;
  font-size: 14px;
}

.btn-generate {
  padding: 8px 16px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-generate:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-generate:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  color: #f1f5f9;
  margin: 0;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.video-count {
  background: rgba(6, 182, 212, 0.2);
  color: var(--accent-color);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.predicted-videos-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.predicted-video-item {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.video-info {
  padding: 16px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  margin: 16px;
}

.video-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 14px;
  color: #94a3b8;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: #f1f5f9;
  font-weight: 600;
}

.detail-value.status-original {
  color: #10b981;
}

.detail-value.status-predicted {
  color: #3b82f6;
}

/* 加载和错误状态样式 */
.loading-samples,
.no-samples-message,
.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #94a3b8;
}

.loading-samples .loading-spinner,
.no-samples-message .no-samples-icon,
.error-message .error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--accent-color);
}

.loading-samples .loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.no-samples-message h3,
.error-message h3 {
  font-size: 18px;
  color: #f1f5f9;
  margin-bottom: 8px;
}

.no-samples-message p,
.error-message p {
  font-size: 14px;
  color: #94a3b8;
  margin-bottom: 16px;
}

.btn-primary-small {
  padding: 8px 16px;
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-primary-small:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.4);
}

/* 样本图片样式 */
.sample-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.sample-preview {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background: rgba(6, 182, 212, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: var(--accent-color);
  overflow: hidden;
}

@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .mobile-only {
    display: block;
  }

  .nav-title h1 {
    font-size: 18px;
  }

  .nav-title p {
    display: none;
  }

  .dropdown-menu {
    right: auto;
    left: 0;
    min-width: 180px;
  }

  /* 移动端视频对比模态框 */
  .video-comparison-modal .modal-content-enhanced {
    width: 98vw;
    max-width: none;
    max-height: 95vh;
    border-radius: 16px;
  }

  .modal-header-modern {
    padding: 16px 20px;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-left {
    width: 100%;
  }

  .header-right {
    width: 100%;
    justify-content: space-between;
  }

  .video-stats {
    flex-direction: column;
    gap: 8px;
  }

  .modal-body-modern {
    padding: 16px 20px;
  }

  .video-comparison-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .video-card-body {
    height: 200px; /* 移动端较小的高度 */
    min-height: 200px;
  }

  .video-player {
    height: 100%; /* 填满容器 */
  }

  .video-card-header {
    padding: 16px 20px 12px;
  }

  .video-title {
    font-size: 14px;
  }

  .video-card-footer {
    padding: 12px 20px 16px;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .video-info {
    flex-direction: column;
    gap: 8px;
  }

  .modal-footer-modern {
    padding: 16px 20px;
    flex-direction: column;
    gap: 12px;
  }

  .footer-left,
  .footer-right {
    width: 100%;
    justify-content: center;
  }

  .btn-modern {
    flex: 1;
    justify-content: center;
  }

  .modal-title-modern {
    font-size: 20px;
  }

  .fullscreen-video-container {
    width: 95%;
    height: 95%;
  }

  .fullscreen-close-btn {
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}

/* 工具栏容器 */
.toolbar-container {
  margin-bottom: 24px;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(12px);
  border-radius: 16px;
  border: 1px solid rgba(6, 182, 212, 0.2);
  padding: 16px;
}

.toolbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--accent-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quick-action-btn {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid transparent;
  color: #94a3b8;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quick-action-btn:hover {
  color: var(--accent-color);
  background: rgba(6, 182, 212, 0.2);
  border-color: rgba(6, 182, 212, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
}

.toolbar-separator {
  width: 1px;
  height: 24px;
  background: #4b5563;
  margin: 0 8px;
}

/* 路径粗细控制样式 */
.path-thickness-control {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.thickness-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #94a3b8;
  font-size: 12px;
  cursor: pointer;
}

.thickness-label i {
  color: var(--accent-color);
  font-size: 10px;
}

.thickness-slider {
  width: 80px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.thickness-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.thickness-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 8px rgba(6, 182, 212, 0.4);
}

.thickness-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.thickness-slider::-moz-range-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 8px rgba(6, 182, 212, 0.4);
}

.thickness-value {
  font-size: 10px;
  color: var(--accent-color);
  font-weight: 600;
  background: rgba(6, 182, 212, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid rgba(6, 182, 212, 0.3);
  min-width: 24px;
  text-align: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.progress-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-label {
  font-size: 14px;
  color: #9ca3af;
}

.progress-bar-container {
  width: 128px;
  height: 8px;
  background: #374151;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-color), var(--secondary-color));
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-value {
  font-size: 14px;
  color: var(--accent-color);
  font-weight: 500;
}

.performance-monitor {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #9ca3af;
}

.performance-monitor i {
  color: var(--secondary-color);
}

/* 主网格布局 */
.main-grid {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 24px;

  

}

.left-panel {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: 1200px;
  overflow-y: auto;
  overflow-x: hidden;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(6, 182, 212, 0.5) rgba(0, 0, 0, 0.1);
}

/* Webkit浏览器滚动条样式 */
.left-panel::-webkit-scrollbar {
  width: 8px;
}

.left-panel::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.left-panel::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.6), rgba(59, 130, 246, 0.6));
  border-radius: 4px;
  transition: background 0.3s ease;
}

.left-panel::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.8), rgba(59, 130, 246, 0.8));
}

/* 面板样式 */
.panel-container {
  background: var(--panel-bg);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
  border: 1px solid var(--panel-border);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(12px);
  transition: all 0.3s ease;
}

.panel-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.5);
  border-color: var(--panel-border-hover);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.panel-title {
  color: var(--accent-color);
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 状态徽章 */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-active {
  background: rgba(16, 185, 129, 0.2);
  color: var(--success-color);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-playing {
  background: rgba(6, 182, 212, 0.2);
  color: var(--accent-color);
  border: 1px solid rgba(6, 182, 212, 0.3);
}

.status-paused {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning-color);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

/* 面板操作按钮 */
.panel-action-btn {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.panel-action-btn:hover {
  color: var(--accent-color);
  background: rgba(6, 182, 212, 0.2);
}

/* 信息卡片 */
.info-card {
  background: rgba(6, 182, 212, 0.1);
  border: 1px solid rgba(6, 182, 212, 0.2);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #d1d5db;
}

.info-card i {
  color: var(--accent-color);
}

/* 按钮组 */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 新的按钮样式 */
.tech-button-primary {
  width: 100%;
  padding: 12px 16px;
  border-radius: 12px;
  font-weight: 500;
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
}

.tech-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(6, 182, 212, 0.4);
}

.tech-button-secondary {
  width: 100%;
  padding: 12px 16px;
  border-radius: 12px;
  font-weight: 500;
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.tech-button-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.1);
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: var(--accent-color);
  display: block;
}

.stat-label {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 4px;
  display: block;
}

/* 路径点部分 */
.points-section {
  margin-top: 24px;
}

.points-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.points-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--accent-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.points-count {
  font-size: 12px;
  color: #9ca3af;
}

.points-container {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  max-height: 800px;
  overflow-y: auto;
  overflow-x: hidden;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(6, 182, 212, 0.5) rgba(0, 0, 0, 0.1);
}

/* 路径点详情滚动条样式 */
.points-container::-webkit-scrollbar {
  width: 6px;
}

.points-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.points-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.6), rgba(59, 130, 246, 0.6));
  border-radius: 3px;
  transition: background 0.3s ease;
}

.points-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.8), rgba(59, 130, 246, 0.8));
}

/* 主工作区域 */
.main-workspace {
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: 1200px;
}

.workspace-panel {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(16px);
  border-radius: 16px;
  border: 1px solid rgba(6, 182, 212, 0.2);
  padding: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.workspace-header {
  margin-bottom: 24px;
}

.workspace-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.workspace-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
}

.workspace-title-text {
  flex: 1;
}

.workspace-title {
  font-size: 20px;
  font-weight: bold;
  color: white;
  margin: 0;
}

.workspace-subtitle {
  font-size: 14px;
  color: #9ca3af;
  margin: 4px 0 0 0;
}

.workspace-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

/* 编辑工具栏 */
.edit-toolbar {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  padding: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 2D/3D视图切换标签页 */
.view-mode-tabs {
  display: flex;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  padding: 2px;
  gap: 2px;
}

.view-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  justify-content: center;
}

.view-tab:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.view-tab.active {
  background: #06b6d4;
  color: white;
  box-shadow: 0 2px 4px rgba(6, 182, 212, 0.3);
}

.view-tab i {
  font-size: 14px;
}

.view-tab span {
  font-size: 12px;
}

.toolbar-btn {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: transparent;
  border: 1px solid transparent;
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-btn:hover {
  color: var(--accent-color);
  background: rgba(6, 182, 212, 0.2);
  border-color: rgba(6, 182, 212, 0.3);
}

.toolbar-btn.active {
  color: var(--accent-color);
  background: rgba(6, 182, 212, 0.2);
  border-color: rgba(6, 182, 212, 0.3);
}

/* 视图控制 */
.view-controls {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  padding: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.view-control-btn {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: transparent;
  border: 1px solid transparent;
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-control-btn:hover {
  color: var(--secondary-color);
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
}

.view-control-btn.active {
  color: var(--secondary-color);
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
}

/* 工作区状态栏 */
.workspace-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 12px;
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #9ca3af;
}

.status-item i {
  color: var(--accent-color);
}

/* 工作区画布 */
.workspace-canvas-container {
  position: relative;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  min-height: 500px;
}

.workspace-canvas {
  width: 100%;
  height: 100%;
  min-height: 500px;
  position: relative;
}

/* 场景切换 */
.scene-3d, .scene-2d {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.3s ease;
}

.scene-3d.active, .scene-2d.active {
  opacity: 1;
  pointer-events: auto;
  z-index: 1;
}

.scene-3d:not(.active), .scene-2d:not(.active) {
  opacity: 0;
  pointer-events: none;
  z-index: 0;
}

/* 2D Canvas样式 */
.canvas-2d {
  width: 100%;
  height: 100%;
  background: #0a0a0a;
  border-radius: 8px;
  cursor: crosshair;
}

/* 2D场景控制面板 */
.canvas-2d-controls {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  font-weight: 500;
}

.control-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.control-btn:active {
  transform: scale(0.95);
}

#zoom-level-2d {
  color: #06b6d4;
  font-weight: 600;
  font-size: 12px;
  min-width: 40px;
  text-align: center;
}

/* 2D场景状态信息 */
.canvas-2d-status {
  position: absolute;
  bottom: 16px;
  left: 16px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  display: flex;
  gap: 16px;
}

.canvas-2d-status .status-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.canvas-2d-status .status-label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 11px;
  font-weight: 500;
}

.canvas-2d-status .status-value {
  color: #06b6d4;
  font-size: 12px;
  font-weight: 600;
}

.workspace-overlay {
  position: absolute;
  top: 16px;
  left: 16px;
  pointer-events: none;
}

.workspace-info {
  pointer-events: auto;
}

.info-panel {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(12px);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid rgba(6, 182, 212, 0.2);
  max-width: 300px;
}

.info-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--accent-color);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-list {
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 12px;
  color: #d1d5db;
}

.info-list li {
  margin-bottom: 4px;
}

/* 视频面板 */
.video-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.video-wrapper {
  position: relative;
  background: black;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  aspect-ratio: 16/9;
}

.video-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 16px;
}

.video-wrapper:hover .video-overlay {
  opacity: 1;
}

.video-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.video-control-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  border: none;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-control-btn:hover {
  background: var(--accent-color);
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(6, 182, 212, 0.4);
}

.video-info {
  color: white;
}

.video-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.video-stats {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #d1d5db;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
}

.progress-bar {
  position: relative;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--accent-color);
  border-radius: 2px;
  transition: width 0.3s ease;
  width: 30%;
}

.progress-handle {
  position: absolute;
  top: 50%;
  width: 12px;
  height: 12px;
  background: var(--accent-color);
  border-radius: 50%;
  transform: translateY(-50%);
  transition: left 0.3s ease;
  left: 30%;
}

.video-analysis {
  margin-top: 16px;
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  font-size: 12px;
}

.analysis-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #d1d5db;
}

.analysis-item i {
  color: var(--accent-color);
}

/* 视频占位符 */
.video-placeholder {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(59, 130, 246, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  border: 2px dashed rgba(6, 182, 212, 0.3);
}

.placeholder-content {
  text-align: center;
  color: #d1d5db;
}

.placeholder-icon {
  font-size: 48px;
  color: var(--accent-color);
  margin-bottom: 16px;
  opacity: 0.7;
}

.placeholder-content h4 {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin: 0 0 8px 0;
}

.placeholder-content p {
  font-size: 14px;
  color: #9ca3af;
  margin: 0 0 16px 0;
}

.placeholder-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.placeholder-stats span {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.placeholder-stats i {
  color: var(--accent-color);
}

/* 3D场景占位符 */
.scene-placeholder {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(30, 41, 59, 0.3));
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  border: 2px dashed rgba(6, 182, 212, 0.2);
}

.scene-content {
  text-align: center;
  color: #d1d5db;
  max-width: 400px;
}

.scene-icon {
  font-size: 64px;
  color: var(--accent-color);
  margin-bottom: 24px;
  opacity: 0.8;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.scene-content h3 {
  font-size: 24px;
  font-weight: bold;
  color: white;
  margin: 0 0 12px 0;
}

.scene-content > p {
  font-size: 16px;
  color: #9ca3af;
  margin: 0 0 32px 0;
}

.scene-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-bottom: 32px;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(6, 182, 212, 0.2);
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(6, 182, 212, 0.4);
  transform: translateY(-2px);
}

.feature-item i {
  font-size: 20px;
  color: var(--accent-color);
}

.feature-item span {
  font-size: 12px;
  color: #d1d5db;
  font-weight: 500;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(6, 182, 212, 0.2);
}

.loading-indicator span {
  font-size: 14px;
  color: #9ca3af;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .scene-features {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .scene-icon {
    font-size: 48px;
  }

  .scene-content h3 {
    font-size: 20px;
  }

  .placeholder-icon {
    font-size: 36px;
  }

  .placeholder-content h4 {
    font-size: 16px;
  }
}

/* 底部面板 */
.bottom-panels {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.info-panel-card {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(12px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 16px;
  transition: all 0.3s ease;
}

.info-panel-card:hover {
  border-color: rgba(6, 182, 212, 0.3);
}

.info-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.info-panel-header i {
  color: var(--accent-color);
  margin-right: 8px;
}

.info-panel-header h3 {
  font-size: 14px;
  font-weight: 500;
  color: white;
  margin: 0;
  display: flex;
  align-items: center;
}

.panel-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  background: rgba(6, 182, 212, 0.2);
  color: var(--accent-color);
  border: 1px solid rgba(6, 182, 212, 0.3);
}

.panel-badge.success {
  background: rgba(16, 185, 129, 0.2);
  color: var(--success-color);
  border-color: rgba(16, 185, 129, 0.3);
}

.clear-history-btn {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: var(--danger-color);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

.clear-history-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.4);
}

.info-panel-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 统计项样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(6, 182, 212, 0.3);
}

.stat-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.2), rgba(59, 130, 246, 0.2));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-color);
  font-size: 14px;
}

.stat-data {
  flex: 1;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: var(--accent-color);
  display: block;
  line-height: 1;
}

.stat-label {
  font-size: 11px;
  color: #9ca3af;
  margin-top: 2px;
  display: block;
}

/* 性能指标 */
.performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.metric-label {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 500;
}

.metric-bar {
  height: 6px;
  background: #374151;
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.metric-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
  position: relative;
}

.metric-fill.success {
  background: linear-gradient(90deg, var(--success-color), #22c55e);
}

.metric-fill.warning {
  background: linear-gradient(90deg, var(--warning-color), #fbbf24);
}

.metric-fill.danger {
  background: linear-gradient(90deg, var(--danger-color), #f87171);
}

.metric-value {
  font-size: 12px;
  color: var(--accent-color);
  font-weight: 600;
}

/* 活动列表 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(6, 182, 212, 0.2);
}

.activity-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  flex-shrink: 0;
}

.activity-icon.success {
  background: rgba(16, 185, 129, 0.2);
  color: var(--success-color);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.activity-icon.warning {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning-color);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.activity-icon.info {
  background: rgba(6, 182, 212, 0.2);
  color: var(--accent-color);
  border: 1px solid rgba(6, 182, 212, 0.3);
}

.activity-icon.danger {
  background: rgba(239, 68, 68, 0.2);
  color: var(--danger-color);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.activity-text {
  font-size: 12px;
  color: #d1d5db;
  font-weight: 500;
}

.activity-time {
  font-size: 10px;
  color: #6b7280;
}

/* 键盘快捷键样式 */
kbd {
  display: inline-block;
  padding: 2px 6px;
  font-size: 11px;
  font-family: monospace;
  background: #374151;
  color: #d1d5db;
  border-radius: 4px;
  border: 1px solid #4b5563;
}

/* 响应式设计 */
@media (max-width: 1280px) {
  .main-grid {
    grid-template-columns: 300px 1fr;
    gap: 20px;
  }

  .workspace-panel {
    padding: 20px;
  }

  .edit-toolbar {
    flex-wrap: wrap;
  }

  .workspace-canvas-container {
    min-height: 400px;
  }
}

@media (max-width: 1024px) {
  .main-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .left-panel {
    order: 2;
    max-height: 1200px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .main-workspace {
    order: 1;
  }

  .toolbar-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .toolbar-right {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  /* 隐藏桌面导航 */
  .nav-link {
    display: none;
  }

  /* 移动端工具栏 */
  .edit-toolbar {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
  }

  .toolbar-btn {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  /* 移动端面板 */
  .panel-container,
  .workspace-panel {
    padding: 16px;
    border-radius: 8px;
  }

  .workspace-title {
    font-size: 18px;
  }

  .workspace-subtitle {
    display: none;
  }

  /* 移动端工作区 */
  .workspace-canvas-container {
    min-height: 600px;
  }

  .workspace-overlay {
    display: none;
  }

  /* 移动端底部面板 */
  .bottom-panels {
    grid-template-columns: 1fr;
  }

  /* 移动端统计 */
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  /* 超小屏幕优化 */
  .workspace-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }

  .quick-action-btn {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .toolbar-btn {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }

  .workspace-canvas-container {
    min-height: 250px;
  }

  .toolbar-container {
    padding: 12px;
  }

  .workspace-panel {
    padding: 12px;
  }
}

/* 路径点列表样式 - 重新设计 */
.no-sample-message {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
}

.no-sample-icon {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.no-sample-message h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.no-sample-message p {
    margin-bottom: 1.5rem;
}

.btn-primary-small {
    background: var(--accent-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-primary-small:hover {
    background: #0891b2;
    transform: translateY(-1px);
}

/* 样本头部 */
.sample-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem;
    background: rgba(6, 182, 212, 0.1);
    border: 1px solid rgba(6, 182, 212, 0.3);
    border-radius: 8px;
    margin-bottom: 1rem;
}

.sample-info {
    flex: 1;
}

.sample-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.sample-meta {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.sample-type {
    background: rgba(59, 130, 246, 0.2);
    color: #60a5fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
}

.sample-difficulty {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.difficulty-easy {
    background: rgba(34, 197, 94, 0.2);
    color: #4ade80;
}

.difficulty-medium {
    background: rgba(245, 158, 11, 0.2);
    color: #fbbf24;
}

.difficulty-hard {
    background: rgba(239, 68, 68, 0.2);
    color: #f87171;
}

.sample-description {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin: 0;
}

.change-sample-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.change-sample-btn:hover {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

/* 路径列表 */
.paths-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.path-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.path-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
}

.path-item.active {
    background: rgba(6, 182, 212, 0.15);
    border-color: var(--accent-color);
}

.path-item.hidden {
    opacity: 0.6;
}

.path-header {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    cursor: pointer;
}

.path-indicator {
    width: 4px;
    height: 40px;
    border-radius: 2px;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.path-info {
    flex: 1;
}

.path-name-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.25rem;
}

.path-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95rem;
}

.path-index {
    font-size: 0.8rem;
    color: var(--text-secondary);
    background: rgba(255, 255, 255, 0.1);
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
}

.path-details {
    display: flex;
    gap: 0.75rem;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.path-type {
    background: rgba(139, 92, 246, 0.2);
    color: #a78bfa;
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
}

.path-points {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.path-actions {
    display: flex;
    gap: 0.5rem;
}

.path-action-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.path-action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

.path-action-btn.video-btn {
    color: #f59e0b;
}

.path-action-btn.video-btn:hover {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.visibility-btn.active {
    background: var(--accent-color);
    color: white;
}

.edit-btn:hover {
    background: #f59e0b;
    color: white;
}

/* 点列表 */
.points-list {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.2);
}

.points-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.points-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.points-count {
    font-size: 0.8rem;
    color: var(--text-secondary);
    background: rgba(255, 255, 255, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}



.point-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.point-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.point-item.editable:hover {
    border-color: var(--accent-color);
}

.point-item.locked {
    opacity: 0.7;
    cursor: default;
}

.point-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-right: 0.75rem;
}

.point-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.point-number {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-secondary);
    min-width: 20px;
    text-align: center;
}

.point-info {
    flex: 1;
}

.point-coords {
    margin-bottom: 0.25rem;
}

.coord-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-right: 0.5rem;
}

.coord-values {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
}

.point-details {
    display: flex;
    gap: 0.75rem;
    font-size: 0.8rem;
}

.point-angle,
.point-speed {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: var(--text-secondary);
}

.point-status {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.125rem 0.375rem;
    border-radius: 4px;
    font-weight: 500;
}

.point-status.editable {
    background: rgba(34, 197, 94, 0.2);
    color: #4ade80;
}

.point-status.locked {
    background: rgba(107, 114, 128, 0.2);
    color: #9ca3af;
}

.point-actions {
    display: flex;
    gap: 0.25rem;
}

.point-action-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.point-action-btn:hover:not(.disabled) {
    background: var(--accent-color);
    color: white;
}

.point-action-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 工作区覆盖层切换按钮 */
.overlay-toggle-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: rgba(6, 182, 212, 0.1);
    border: 1px solid rgba(6, 182, 212, 0.3);
    border-radius: 6px;
    color: var(--accent-color);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    margin-right: 1rem;
}

.overlay-toggle-btn:hover {
    background: rgba(6, 182, 212, 0.2);
    border-color: rgba(6, 182, 212, 0.5);
}

.overlay-toggle-btn.hidden {
    background: rgba(107, 114, 128, 0.1);
    border-color: rgba(107, 114, 128, 0.3);
    color: var(--text-secondary);
}

.overlay-toggle-btn.hidden:hover {
    background: rgba(107, 114, 128, 0.2);
    border-color: rgba(107, 114, 128, 0.5);
}

/* 路径图例样式更新 */
.legend-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.legend-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.legend-item.hidden {
    opacity: 0.5;
    background: rgba(255, 255, 255, 0.02);
}

.legend-item.hidden .legend-color {
    opacity: 0.6;
}

.legend-item.hidden .legend-name,
.legend-item.hidden .legend-points {
    color: var(--text-secondary);
    opacity: 0.7;
}

.legend-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.legend-name {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-primary);
}

.legend-points {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.legend-actions {
    display: flex;
    gap: 0.25rem;
}

.legend-toggle,
.legend-generate {
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.legend-toggle:hover {
    background: var(--accent-color);
    color: white;
}

.legend-toggle.active {
    background: var(--accent-color);
    color: white;
}

.legend-generate:hover {
    background: #10b981;
    color: white;
}

/* 视频模态框全屏样式 */
.modal-fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 10000 !important;
    background: rgba(0, 0, 0, 0.95) !important;
}

.modal-fullscreen .modal-content-enhanced {
    width: 100% !important;
    height: 100% !important;
    max-width: none !important;
    max-height: none !important;
    margin: 0 !important;
    border-radius: 0 !important;
    display: flex !important;
    flex-direction: column !important;
}

.modal-fullscreen .video-modal-body {
    flex: 1 !important;
    overflow-y: auto !important;
}

.modal-fullscreen .video-comparison-grid {
    height: calc(100vh - 300px) !important;
}

.modal-fullscreen .video-container {
    height: 100% !important;
    aspect-ratio: unset !important;
}

/* 模态框头部操作按钮 */
.modal-header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-action-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-action-btn:hover {
    background: var(--accent-color);
    color: white;
}

/* 视频生成模态框样式 */
.video-generation-modal .modal-content-enhanced {
    max-width: 1200px;
    width: 95vw;
    max-height: 90vh;
}

.video-modal-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.video-modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
}

.video-generation-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 路径信息区域 */
.path-info-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1.5rem;
}

.path-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
}

.detail-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.detail-value {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-primary);
}

/* 视频对比区域 */
.video-comparison-section {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 1.5rem;
}

.video-comparison-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.video-panel {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    overflow: hidden;
}

.video-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.video-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.video-controls {
    display: flex;
    gap: 0.5rem;
}

.video-control-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-control-btn:hover {
    background: var(--accent-color);
    color: white;
}

.video-container {
    position: relative;
    aspect-ratio: 16/9;
    background: #000;
}

.comparison-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 1rem;
}

.video-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.video-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: white;
}

.video-duration {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
}

/* 视频占位符 */
.video-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1e293b, #334155);
}

.placeholder-content {
    text-align: center;
    color: white;
}

.placeholder-icon {
    font-size: 3rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.placeholder-content h4 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.placeholder-content p {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.5rem;
}

.generation-progress {
    width: 200px;
    margin: 0 auto;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-color), var(--secondary-color));
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.8rem;
    color: var(--accent-color);
    font-weight: 600;
}

/* 生成选项区域 */
.generation-options-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1.5rem;
}

.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.option-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-primary);
}

.option-select {
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.option-select:focus {
    outline: none;
    border-color: var(--accent-color);
    background: rgba(255, 255, 255, 0.15);
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    border-left: 4px solid var(--accent-color);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 10000;
    max-width: 600px;
     height:80px;
     line-height: 60px;
    
}

.notification.show {
    transform: translateX(0);
    height: 60px;
}

.notification-success {
    border-left-color: #10b981;
     height: 60px;
}

.notification-error {
    border-left-color: #ef4444;
     height: 60px;
}

.notification-info {
    border-left-color: var(--accent-color);
     height: 60px;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
     height: 60px;
}

.notification-content i {
    font-size: 1.2rem;

}

/* 响应式设计 */
@media (max-width: 768px) {
    .video-comparison-grid {
        grid-template-columns: 1fr;
    }

    .path-details-grid {
        grid-template-columns: 1fr;
    }

    .options-grid {
        grid-template-columns: 1fr;
    }

    .video-generation-modal .modal-content-enhanced {
        width: 95vw;
        margin: 1rem;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(6, 182, 212, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(6, 182, 212, 0.7);
}

/* 选择文本样式 */
::selection {
  background: rgba(6, 182, 212, 0.3);
  color: white;
}

/* 焦点样式 */
*:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

button:focus,
input:focus,
select:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--accent-color);
}

/* 禁用状态 */
.disabled,
[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #374151;
  border-top-color: var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 模态框样式 */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
}

.modal-content-enhanced {
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(16px);
  border-radius: 16px;
  border: 1px solid rgba(6, 182, 212, 0.3);
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
}

.modal-title {
  font-size: 20px;
  font-weight: bold;
  color: white;
  margin: 0;
}

.modal-subtitle {
  font-size: 14px;
  color: #9ca3af;
  margin: 4px 0 0 0;
}

.close-button-enhanced {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.close-button-enhanced:hover {
  color: var(--danger-color);
  background: rgba(239, 68, 68, 0.2);
  transform: rotate(90deg);
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

/* 样本网格 */
.sample-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.sample-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sample-card:hover {
  border-color: rgba(6, 182, 212, 0.3);
  background: rgba(255, 255, 255, 0.1);
}

.sample-card.selected {
  border-color: var(--accent-color);
  background: rgba(6, 182, 212, 0.1);
}

.sample-preview {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.2), rgba(59, 130, 246, 0.2));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-color);
  font-size: 20px;
  margin-bottom: 12px;
}

.sample-info h3 {
  font-weight: 500;
  color: white;
  margin: 0 0 4px 0;
}

.sample-info p {
  font-size: 14px;
  color: #9ca3af;
  margin: 0 0 8px 0;
}

.sample-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #6b7280;
}

.sample-status {
  margin-top: 12px;
}

/* 按钮样式 */
.btn-primary {
  padding: 12px 24px;
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(6, 182, 212, 0.4);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.1);
  color: #d1d5db;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

.btn-danger {
  padding: 12px 24px;
  background: linear-gradient(135deg, var(--danger-color), #dc2626);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

/* 操作指南样式 */
.guide-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.guide-section {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.guide-section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--accent-color);
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.guide-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.guide-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.guide-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(6, 182, 212, 0.2);
}

.guide-item kbd {
  min-width: 80px;
  text-align: center;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(6, 182, 212, 0.3);
  color: var(--accent-color);
  font-weight: 500;
}

.guide-item span {
  color: #d1d5db;
  font-size: 14px;
}

.guide-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

/* 路径点样式 */
.path-point-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.path-point-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(6, 182, 212, 0.3);
}

.point-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

.point-info {
  flex: 1;
}

.point-name {
  font-size: 14px;
  font-weight: 500;
  color: white;
  margin-bottom: 4px;
}

.point-coords {
  font-size: 11px;
  color: #9ca3af;
  margin-bottom: 2px;
}

.point-angle {
  font-size: 11px;
  color: #6b7280;
}

.point-actions {
  display: flex;
  gap: 4px;
}

.point-action-btn {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

.point-action-btn:hover {
  background: rgba(6, 182, 212, 0.2);
  border-color: rgba(6, 182, 212, 0.3);
  color: var(--accent-color);
}

.point-action-btn:last-child:hover {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
  color: var(--danger-color);
}

.container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 20px;
  max-width: 1600px;
  margin: 0 auto;
  height: 100vh;
}

.left-panel, .right-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.panel-container {
  background: var(--panel-bg);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
  border: 1px solid var(--panel-border);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(12px);
  transition: all 0.3s ease;
}

/* Enhanced glow effect on panels */
.panel-container:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
}

.panel-container:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 15%;
  right: 15%;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--secondary-color), transparent);
  opacity: 0.4;
}

.panel-container:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.5);
}

.panel-title {
  color: var(--accent-color);
  margin-bottom: 18px;
  font-weight: 600;
  position: relative;
  display: inline-block;
  text-shadow: 0 0 10px rgba(6, 182, 212, 0.3);
  letter-spacing: 0.5px;
}

.panel-title:after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
}

p {
  margin-bottom: 15px;
  line-height: 1.6;
}

/* 新的按钮样式 */
.tech-button-primary {
  @apply w-full px-4 py-3 rounded-xl font-medium text-white transition-all duration-300 flex items-center justify-between;
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  box-shadow:
    0 4px 15px rgba(6, 182, 212, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.tech-button-primary:hover {
  transform: translateY(-2px);
  box-shadow:
    0 6px 20px rgba(6, 182, 212, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.tech-button-secondary {
  @apply w-full px-4 py-3 rounded-xl font-medium text-white transition-all duration-300 flex items-center justify-between;
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
  box-shadow:
    0 4px 15px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.tech-button-secondary:hover {
  transform: translateY(-2px);
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 保留原有按钮样式 */
.tech-button {
  background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: bold;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  box-shadow:
    0 4px 0 rgba(0, 0, 0, 0.2),
    0 5px 15px var(--button-shadow),
    inset 0 -2px 0 rgba(0, 0, 0, 0.1),
    inset 0 2px 0 rgba(255, 255, 255, 0.2);
}

.tech-button:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 6px 0 rgba(0, 0, 0, 0.2),
    0 8px 20px var(--button-shadow),
    inset 0 -2px 0 rgba(0, 0, 0, 0.1),
    inset 0 2px 0 rgba(255, 255, 255, 0.2);
}

.tech-button:active {
  transform: translateY(2px);
  box-shadow: 
    0 1px 0 rgba(0, 0, 0, 0.2),
    0 2px 10px var(--button-shadow),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.tech-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: all 0.5s ease;
}

.tech-button:hover::before {
  left: 100%;
}

.video-container {
  width: 100%;
  height: 300px;
  background-color: rgba(15, 23, 42, 0.6);
  border: 1px dashed rgba(6, 182, 212, 0.5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.5);
  position: relative;
}

.video-container:after {
  content: '视频加载中...';
  position: absolute;
}

.points-container {
  margin-top: 20px;
  min-height: 100%;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  padding: 15px;
  border: 1px solid rgba(30, 136, 229, 0.3);
}

/* Point visualization styles */
.point-item {
  background: rgba(30, 41, 59, 0.6);
  padding: 14px;
  margin-bottom: 12px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-left: 3px solid var(--secondary-color);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.point-item:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(6, 182, 212, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.point-item:hover:before {
  opacity: 1;
}

.point-item:hover {
  background: rgba(30, 41, 59, 0.8);
  transform: translateX(5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.point-info {
  display: flex;
  gap: 15px;
}

.point-actions {
  display: flex;
  gap: 10px;
}

.edit-point, .delete-point {
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.edit-point {
  color: var(--accent-color);
}

.delete-point {
  color: #f43f5e;
}

.edit-point:hover, .delete-point:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* Modal styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
}

.modal-content {
  background: var(--modal-bg);
  width: 450px;
  max-width: 90%;
  border-radius: 12px;
  padding: 28px;
  position: relative;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(6, 182, 212, 0.3);
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 24px;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-button:hover {
  color: var(--accent-color);
  transform: rotate(90deg);
}

.form-group {
  margin-bottom: 18px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: var(--accent-color);
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 12px 16px;
  background: rgba(15, 23, 42, 0.4);
  border: 1px solid rgba(59, 130, 246, 0.5);
  border-radius: 6px;
  color: var(--text-color);
  outline: none;
  transition: all 0.3s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.form-group input:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 15px rgba(6, 182, 212, 0.2), inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Sample buttons */
.sample-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 24px;
}

.sample-option {
  padding: 18px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: bold;
  color: white;
  transition: all 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: 
    0 4px 0 rgba(0, 0, 0, 0.2),
    0 5px 10px rgba(0, 0, 0, 0.15),
    inset 0 -2px 0 rgba(0, 0, 0, 0.1),
    inset 0 2px 0 rgba(255, 255, 255, 0.1);
}

.sample-option:hover {
  transform: translateY(-3px);
  box-shadow: 
    0 6px 0 rgba(0, 0, 0, 0.2),
    0 8px 15px rgba(0, 0, 0, 0.15),
    inset 0 -2px 0 rgba(0, 0, 0, 0.1),
    inset 0 2px 0 rgba(255, 255, 255, 0.1);
}

.sample-option:active {
  transform: translateY(2px);
  box-shadow: 
    0 1px 0 rgba(0, 0, 0, 0.2),
    0 2px 5px rgba(0, 0, 0, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.sample-option:nth-child(1) {
  background: linear-gradient(135deg, #f97316, #f43f5e);
}

.sample-option:nth-child(2) {
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
}

.sample-option:nth-child(3) {
  background: linear-gradient(135deg, #10b981, #84cc16);
}

.sample-option:nth-child(4) {
  background: linear-gradient(135deg, #9333ea, #ec4899);
}

.sample-option:nth-child(5) {
  background: linear-gradient(135deg, #f59e0b, #ef4444);
}

/* Notification */
.notification {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background: var(--success-color);
  color: white;
  padding: 15px 25px;
  border-radius: 8px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  transform: translateY(100px);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 1001;
}

.notification.show {
  transform: translateY(0);
  opacity: 1;
}

/* Video player styles */
.video-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  padding-bottom: 45%; /* 16:9 ratio */
  height: 0;
  background-color: #0f172a;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.video-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 59%;
  height: auto;
  object-fit: cover;
}

.video-control-btn {
  background-color: rgba(15, 23, 42, 0.7);
  color: white;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.video-control-btn:hover {
  background-color: var(--accent-color);
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(6, 182, 212, 0.4);
}

/* Fullscreen state */
.fullscreen-container {
  position: fixed !important;
  top: 0;
  left: 0;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999;
  background-color: rgba(15, 23, 42, 0.95);
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.fullscreen-container > div {
  height: 100%;
}

.fullscreen-container .video-player {
  max-height: calc(100vh - 100px);
}

/* Angle control styles */
.angle-control {
  position: absolute;
  width: 120px;
  height: 120px;
  border: 2px solid var(--accent-color);
  border-radius: 50%;
  cursor: grab;
  display: none;
  z-index: 100;
  background-color: rgba(15, 23, 42, 0.8);
  box-shadow: 0 0 20px rgba(6, 182, 212, 0.5);
}

.angle-control.active {
  display: block;
}

.angle-control:active {
  cursor: grabbing;
}

.angle-control-handle {
  position: absolute;
  width: 16px;
  height: 16px;
  background-color: var(--accent-color);
  border-radius: 50%;
  top: 0;
  left: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  box-shadow: 0 0 12px rgba(6, 182, 212, 0.8);
}

.angle-control-line {
  position: absolute;
  width: 2px;
  height: 50%;
  background-color: var(--accent-color);
  bottom: 50%;
  left: 50%;
  transform: translateX(-50%);
  transform-origin: bottom center;
}

.angle-value-display {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1rem;
  color: var(--accent-color);
  background-color: rgba(15, 23, 42, 0.7);
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
}

/* Angle slider styles */
#angle-slider {
  -webkit-appearance: none;
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: rgba(15, 23, 42, 0.4);
  outline: none;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

#angle-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

#angle-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(6, 182, 212, 0.5);
}

/* Enhanced particle effect */
body:before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-image: 
    radial-gradient(circle at 10% 10%, rgba(6, 182, 212, 0.05) 0%, transparent 5%),
    radial-gradient(circle at 20% 30%, rgba(6, 182, 212, 0.03) 0%, transparent 4%),
    radial-gradient(circle at 30% 70%, rgba(6, 182, 212, 0.04) 0%, transparent 6%),
    radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.02) 0%, transparent 3%),
    radial-gradient(circle at 60% 60%, rgba(6, 182, 212, 0.03) 0%, transparent 5%),
    radial-gradient(circle at 70% 90%, rgba(6, 182, 212, 0.04) 0%, transparent 6%),
    radial-gradient(circle at 90% 80%, rgba(6, 182, 212, 0.02) 0%, transparent 4%);
}

/* Responsive breakpoints */
@media (max-width: 992px) {
  .container {
    grid-template-columns: 1fr;
  }
  
  .video-container {
    height: 250px;
  }
}

/* ThreeJS canvas container */
.canvas-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  border-radius: 5px;
}

/* Loading animation */
.loading {
  position: relative;
}

.loading:after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--accent-color);
  animation: spin 1s infinite linear;
  margin-left: 10px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Path visualization */
.path-line {
  stroke: var(--accent-color);
  stroke-width: 2;
  stroke-dasharray: 5;
  animation: dash 10s linear infinite;
}

@keyframes dash {
  to {
    stroke-dashoffset: -100;
  }
}

/* Tech animations and effects */
@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Enhanced icons */
.fas {
  margin-right: 5px;
}

.point-actions .fas {
  margin-right: 0;
}

/* 视频播放器样式 */
.video-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 5px;
  padding-bottom: 45%; /* 16:9 比例 */
  height: 0;
  background-color: #000;
}

.video-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 59%;
  height: auto;
  object-fit: cover;
}

.video-control-btn {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.video-control-btn:hover {
  background-color: var(--accent-color);
  transform: scale(1.1);
}

/* 全屏状态 */
.fullscreen-container {
  position: fixed !important;
  top: 0;
  left: 0;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.9);
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.fullscreen-container > div {
  height: 100%;
}

.fullscreen-container .video-player {
  max-height: calc(100vh - 100px);
}

/* 角度调整器样式 */
.angle-control {
  position: absolute;
  width: 120px;
  height: 120px;
  border: 2px solid var(--accent-color);
  border-radius: 50%;
  cursor: grab;
  display: none;
  z-index: 100;
  background-color: rgba(10, 25, 41, 0.7);
  box-shadow: 0 0 15px rgba(0, 229, 255, 0.4);
}

.angle-control.active {
  display: block;
}

.angle-control:active {
  cursor: grabbing;
}

.angle-control-handle {
  position: absolute;
  width: 16px;
  height: 16px;
  background-color: var(--accent-color);
  border-radius: 50%;
  top: 0;
  left: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  box-shadow: 0 0 8px rgba(0, 229, 255, 0.8);
}

.angle-control-line {
  position: absolute;
  width: 2px;
  height: 50%;
  background-color: var(--accent-color);
  bottom: 50%;
  left: 50%;
  transform: translateX(-50%);
  transform-origin: bottom center;
}

.angle-value-display {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1rem;
  color: var(--accent-color);
  background-color: rgba(0, 0, 0, 0.5);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 角度滑块样式 */
#angle-slider {
  -webkit-appearance: none;
  height: 8px;
  background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
  border-radius: 4px;
  outline: none;
}

#angle-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--accent-color);
  cursor: pointer;
  box-shadow: 0 0 5px rgba(0, 229, 255, 0.6);
  transition: all 0.2s;
}

#angle-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

/* 模式指示器容器 */
.mode-indicator-container {
  display: flex;
  align-items: center;
  margin-right: 1rem;
}

.mode-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(6, 182, 212, 0.1);
  border: 1px solid rgba(6, 182, 212, 0.3);
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  transition: all 0.3s ease;
}

.mode-indicator:hover {
  background: rgba(6, 182, 212, 0.15);
  border-color: rgba(6, 182, 212, 0.5);
}

.mode-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
  box-shadow: 0 2px 8px rgba(6, 182, 212, 0.3);
}

.mode-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.mode-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
}

.mode-shortcut {
  font-size: 0.75rem;
  color: var(--text-secondary);
  opacity: 0.8;
}

/* 工具栏组 */
.toolbar-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 0.25rem;
}

/* 双击点时显示的调整角度UI */
.angle-adjustment-ui {
  position: absolute;
  width: 150px;
  background-color: rgba(10, 25, 41, 0.9);
  border: 1px solid var(--accent-color);
  border-radius: 5px;
  padding: 10px;
  z-index: 101;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

.angle-adjustment-ui .angle-slider {
  width: 100%;
  margin: 10px 0;
}

.angle-adjustment-ui .angle-value {
  text-align: center;
  font-weight: bold;
  color: var(--accent-color);
}

.angle-adjustment-ui .buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.angle-adjustment-ui .button {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
}

.angle-adjustment-ui .button.apply {
  background-color: var(--accent-color);
}

/* 新增样式 - 主面板和工作区 */

/* 主面板容器 */
.panel-container-main {
  @apply bg-black/30 backdrop-blur-xl rounded-2xl border border-accent/20 p-6 transition-all duration-300;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.panel-container-main:hover {
  border-color: var(--panel-border-hover);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(6, 182, 212, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 面板头部 */
.panel-header {
  @apply mb-6;
}

.panel-icon {
  @apply w-12 h-12 rounded-xl bg-gradient-to-r from-accent to-secondary flex items-center justify-center text-white text-xl;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
}

.panel-title-main {
  @apply text-xl font-bold text-white;
}

.panel-subtitle {
  @apply text-sm text-gray-400 mt-1;
}

/* 工具栏 */
.toolbar {
  @apply flex items-center space-x-1 bg-black/20 rounded-xl p-2 border border-white/10;
}

.toolbar-btn {
  @apply w-10 h-10 rounded-lg bg-transparent hover:bg-accent/20 text-gray-400 hover:text-accent transition-all duration-300 flex items-center justify-center border border-transparent hover:border-accent/30;
}

.toolbar-btn.active {
  @apply bg-accent/20 text-accent border-accent/30;
}

.toolbar-separator {
  @apply w-px h-6 bg-gray-600 mx-1;
}

/* 视图控制 */
.view-controls {
  @apply flex items-center space-x-1 bg-black/20 rounded-xl p-2 border border-white/10;
}

.view-control-btn {
  @apply w-10 h-10 rounded-lg bg-transparent hover:bg-secondary/20 text-gray-400 hover:text-secondary transition-all duration-300 flex items-center justify-center border border-transparent hover:border-secondary/30;
}

.view-control-btn.active {
  @apply bg-secondary/20 text-secondary border-secondary/30;
}

/* 状态栏 */
.status-bar {
  @apply mt-4 pt-4 border-t border-white/10;
}

.status-item {
  @apply flex items-center text-gray-400;
}

/* 工作区 */
.workspace {
  @apply relative bg-black/20 rounded-xl border border-white/10 overflow-hidden;
  min-height: 500px;
}

.workspace-canvas {
  @apply w-full h-full;
  min-height: 500px;
}

.workspace-overlay {
  @apply absolute top-4 left-4 pointer-events-none;
}

.workspace-info {
  @apply pointer-events-auto;
}

.info-panel {
  @apply bg-black/50 backdrop-blur-md rounded-lg p-4 border border-accent/20 max-w-xs;
}

/* 信息面板卡片 */
.info-panel-card {
  @apply bg-black/20 backdrop-blur-md rounded-xl border border-white/10 p-4 hover:border-accent/30 transition-all duration-300;
}

.info-panel-header {
  @apply flex items-center space-x-2 mb-3;
}

.info-panel-header h3 {
  @apply text-sm font-medium text-white;
}

.info-panel-content {
  @apply space-y-3;
}

/* 统计网格 */
.stat-grid {
  @apply grid grid-cols-3 gap-3;
}

.stat-item {
  @apply text-center;
}

.stat-item .stat-value {
  @apply block text-lg font-bold text-accent;
}

.stat-item .stat-label {
  @apply block text-xs text-gray-400 mt-1;
}

/* 性能指标 */
.performance-metrics {
  @apply space-y-3;
}

.metric {
  @apply flex items-center space-x-3;
}

.metric-label {
  @apply text-xs text-gray-400 w-16 flex-shrink-0;
}

.metric-bar {
  @apply flex-1 h-2 bg-gray-700 rounded-full overflow-hidden;
}

.metric-fill {
  @apply h-full bg-gradient-to-r from-accent to-secondary transition-all duration-300;
}

.metric-value {
  @apply text-xs text-accent font-medium w-8 text-right;
}

/* 活动列表 */
.activity-list {
  @apply space-y-2;
}

.activity-item {
  @apply flex items-center space-x-2 text-xs;
}

.activity-item i {
  @apply w-4 text-center;
}

.activity-time {
  @apply text-gray-500 ml-auto;
}

/* 增强的路径点容器 */
.points-container-enhanced {
  @apply bg-black/20 rounded-lg border border-white/10 p-4 max-h-80 overflow-y-auto;
}

/* 视频面板增强 */
.video-panel {
  @apply space-y-4;
}

.video-wrapper-enhanced {
  @apply relative bg-black rounded-xl overflow-hidden border border-white/10;
  aspect-ratio: 16/9;
}

.video-overlay {
  @apply absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 z-10 flex flex-col justify-between p-4;
}

.video-controls {
  @apply flex items-center space-x-2;
}

.video-control-btn-enhanced {
  @apply w-10 h-10 rounded-full bg-black/50 backdrop-blur-md text-white hover:bg-accent/50 transition-all duration-300 flex items-center justify-center;
}

.video-info {
  @apply text-white;
}

.video-title {
  @apply font-medium mb-1;
}

.video-stats {
  @apply flex items-center space-x-4 text-sm text-gray-300;
}

.video-player-enhanced {
  @apply w-full h-full object-cover;
}

.video-progress {
  @apply absolute bottom-0 left-0 right-0 p-4;
}

.progress-bar {
  @apply relative h-1 bg-white/20 rounded-full overflow-hidden;
}

.progress-fill {
  @apply h-full bg-accent rounded-full transition-all duration-300;
  width: 30%;
}

.progress-handle {
  @apply absolute top-1/2 w-3 h-3 bg-accent rounded-full transform -translate-y-1/2 transition-all duration-300;
  left: 30%;
}

.video-analysis {
  @apply bg-black/20 rounded-lg p-3 border border-white/10;
}

.analysis-item {
  @apply flex items-center space-x-2 text-gray-300;
}

/* 增强的模态框样式 */
.modal-content-enhanced {
  @apply bg-black/90 backdrop-blur-xl rounded-2xl border border-accent/30 max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.5),
    0 0 40px rgba(6, 182, 212, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-white/10;
}

.modal-icon {
  @apply w-12 h-12 rounded-xl bg-gradient-to-r from-accent to-secondary flex items-center justify-center text-white text-xl;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
}

.modal-title {
  @apply text-xl font-bold text-white;
}

.modal-subtitle {
  @apply text-sm text-gray-400 mt-1;
}

.close-button-enhanced {
  @apply w-10 h-10 rounded-lg bg-white/10 hover:bg-danger/20 text-gray-400 hover:text-danger transition-all duration-300 flex items-center justify-center text-xl;
}

.modal-body {
  @apply p-6 max-h-96 overflow-y-auto;
}

.modal-footer {
  @apply flex items-center justify-end space-x-3 p-6 border-t border-white/10 bg-white/5;
}

/* 样本网格 */
.sample-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.sample-card {
  @apply bg-white/5 border border-white/10 rounded-xl p-4 cursor-pointer transition-all duration-300 hover:border-accent/30 hover:bg-white/10;
}

.sample-card.selected {
  @apply border-accent bg-accent/10;
}

.sample-preview {
  @apply w-12 h-12 rounded-lg bg-gradient-to-r from-accent/20 to-secondary/20 flex items-center justify-center text-accent text-xl mb-3;
}

.sample-info h3 {
  @apply font-medium text-white mb-1;
}

.sample-info p {
  @apply text-sm text-gray-400 mb-2;
}

.sample-stats {
  @apply flex items-center space-x-3 text-xs text-gray-500;
}

.sample-status {
  @apply mt-3;
}

/* 表单样式增强 */
.form-section {
  @apply bg-white/5 rounded-xl p-4 border border-white/10;
}

.form-section-title {
  @apply text-sm font-medium text-accent mb-4 flex items-center;
}

.form-group-enhanced {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-300 flex items-center;
}

.form-input-enhanced {
  @apply w-full px-4 py-3 bg-black/30 border border-white/20 rounded-lg text-white placeholder-gray-500 focus:border-accent focus:ring-2 focus:ring-accent/20 transition-all duration-300;
}

.form-help {
  @apply text-xs text-gray-500;
}

/* 角度输入组 */
.angle-input-group {
  @apply space-y-4;
}

.angle-input {
  @apply w-24;
}

.angle-slider-wrapper {
  @apply relative;
}

.angle-slider-enhanced {
  @apply w-full h-2 bg-gray-700 rounded-full appearance-none cursor-pointer;
  background: linear-gradient(90deg, var(--accent-color), var(--secondary-color), var(--accent-color));
}

.angle-slider-enhanced::-webkit-slider-thumb {
  @apply appearance-none w-5 h-5 bg-white rounded-full cursor-pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.angle-marks {
  @apply flex justify-between text-xs text-gray-500 mt-2;
}

.angle-preview {
  @apply flex items-center space-x-4;
}

.angle-indicator {
  @apply relative w-16 h-16 border-2 border-accent/30 rounded-full bg-black/20;
}

.angle-arrow {
  @apply absolute w-1 h-6 bg-accent rounded-full;
  top: 4px;
  left: 50%;
  transform: translateX(-50%);
  transform-origin: bottom center;
}

.angle-value {
  @apply text-lg font-bold text-accent;
}

/* 按钮样式 */
.btn-primary {
  @apply px-6 py-3 bg-gradient-to-r from-accent to-secondary text-white rounded-lg font-medium transition-all duration-300 flex items-center;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(6, 182, 212, 0.4);
}

.btn-secondary {
  @apply px-6 py-3 bg-white/10 text-gray-300 rounded-lg font-medium transition-all duration-300 flex items-center hover:bg-white/20;
}

.btn-danger {
  @apply px-6 py-3 bg-gradient-to-r from-danger to-red-600 text-white rounded-lg font-medium transition-all duration-300 flex items-center;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

/* 响应式设计增强 */
@media (max-width: 1280px) {
  .container {
    @apply px-4;
  }

  .panel-container-main {
    @apply p-4;
  }

  .toolbar {
    @apply flex-wrap;
  }

  .workspace {
    min-height: 400px;
  }
}

@media (max-width: 768px) {
  /* 移动端导航 */
  .nav-link {
    @apply hidden;
  }

  /* 移动端工具栏 */
  .toolbar {
    @apply grid grid-cols-4 gap-2;
  }

  .toolbar-btn {
    @apply w-8 h-8 text-sm;
  }

  /* 移动端面板 */
  .panel-container, .panel-container-main {
    @apply p-3 rounded-lg;
  }

  .panel-title-main {
    @apply text-lg;
  }

  .panel-subtitle {
    @apply hidden;
  }

  /* 移动端工作区 */
  .workspace {
    min-height: 300px;
  }

  .workspace-overlay {
    @apply hidden;
  }

  /* 移动端模态框 */
  .modal-content-enhanced {
    @apply mx-2 max-w-none;
  }

  .modal-header {
    @apply p-4;
  }

  .modal-body {
    @apply p-4;
  }

  .modal-footer {
    @apply p-4 flex-col space-y-2 space-x-0;
  }

  .modal-footer button {
    @apply w-full;
  }

  /* 移动端表单 */
  .sample-grid {
    @apply grid-cols-1;
  }

  .angle-preview {
    @apply flex-col space-x-0 space-y-2;
  }

  /* 移动端统计 */
  .stat-grid {
    @apply grid-cols-1;
  }

  .info-panel-card {
    @apply p-3;
  }
}

@media (max-width: 480px) {
  /* 超小屏幕优化 */
  .panel-icon {
    @apply w-8 h-8 text-base;
  }

  .modal-icon {
    @apply w-8 h-8 text-base;
  }

  .quick-action-btn {
    @apply w-8 h-8 text-sm;
  }

  .toolbar-btn {
    @apply w-6 h-6 text-xs;
  }

  .workspace {
    min-height: 250px;
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  :root {
    --text-color: #f8fafc;
    --text-muted: #cbd5e1;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  :root {
    --panel-border: rgba(6, 182, 212, 0.8);
    --accent-color: #00d4ff;
  }

  .panel-container, .panel-container-main {
    border-width: 2px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 打印样式 */
@media print {
  .nav, .toolbar, .modal, .notification {
    @apply hidden;
  }

  .panel-container, .panel-container-main {
    @apply border border-gray-400 bg-white text-black;
  }

  .workspace {
    @apply border border-gray-400;
  }
}

/* 动画和交互增强 */
@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromBottom {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes glowPulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(6, 182, 212, 0.6);
  }
}

@keyframes rotateIn {
  0% {
    transform: rotate(-180deg) scale(0.8);
    opacity: 0;
  }
  100% {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
}

/* 页面加载动画 */
.animate-slide-in-top {
  animation: slideInFromTop 0.6s ease-out;
}

.animate-slide-in-bottom {
  animation: slideInFromBottom 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
}

.animate-rotate-in {
  animation: rotateIn 0.5s ease-out;
}

/* 悬停效果增强 */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(6, 182, 212, 0.4);
}

/* 加载状态 */
.loading-spinner {
  @apply inline-block w-4 h-4 border-2 border-gray-300 border-t-accent rounded-full;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-dots {
  @apply inline-flex space-x-1;
}

.loading-dots span {
  @apply w-2 h-2 bg-accent rounded-full;
  animation: loadingDots 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 成功/错误状态指示 */
.success-indicator {
  @apply text-success;
  animation: scaleIn 0.3s ease-out;
}

.error-indicator {
  @apply text-danger;
  animation: scaleIn 0.3s ease-out;
}

.warning-indicator {
  @apply text-warning;
  animation: scaleIn 0.3s ease-out;
}

/* 工具提示 */
.tooltip {
  @apply absolute z-50 px-2 py-1 text-xs text-white bg-black rounded opacity-0 pointer-events-none transition-opacity duration-300;
  transform: translateX(-50%);
}

.tooltip.show {
  @apply opacity-100;
}

/* 进度条动画 */
.progress-animated {
  position: relative;
  overflow: hidden;
}

.progress-animated::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressShine 2s infinite;
}

@keyframes progressShine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 脉冲效果 */
.pulse-effect {
  animation: pulseGlow 2s infinite;
}

/* 浮动效果 */
.float-effect {
  animation: float 3s ease-in-out infinite;
}

/* 渐变文字效果 */
.gradient-text {
  background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 玻璃态效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 霓虹效果 */
.neon-effect {
  text-shadow:
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor,
    0 0 20px currentColor;
}

/* 3D按钮效果 */
.button-3d {
  transform-style: preserve-3d;
  transition: transform 0.2s ease;
}

.button-3d:hover {
  transform: translateZ(10px);
}

.button-3d:active {
  transform: translateZ(2px);
}

/* 加载覆盖层 */
.loading-overlay {
  @apply fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50;
}

.loading-content {
  @apply bg-black/90 backdrop-blur-xl rounded-2xl p-8 border border-accent/30 text-center;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
}

.loading-content .loading-spinner {
  @apply w-8 h-8 mx-auto mb-4;
}

.loading-content p {
  @apply text-white text-lg;
}

/* 键盘快捷键提示 */
kbd {
  @apply inline-block px-2 py-1 text-xs font-mono bg-gray-700 text-gray-300 rounded border border-gray-600;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(6, 182, 212, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(6, 182, 212, 0.7);
}

/* 选择文本样式 */
::selection {
  background: rgba(6, 182, 212, 0.3);
  color: white;
}

::-moz-selection {
  background: rgba(6, 182, 212, 0.3);
  color: white;
}

/* 焦点样式 */
*:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

button:focus,
input:focus,
select:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--accent-color);
}

/* 禁用状态 */
.disabled,
[disabled] {
  @apply opacity-50 cursor-not-allowed;
}

/* 隐藏元素 */
.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
}

/* 可访问性增强 */
@media (prefers-reduced-motion: reduce) {
  .animate-slide-in,
  .animate-scale-in,
  .animate-rotate-in,
  .pulse-effect,
  .float-effect {
    animation: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .panel-container,
  .panel-container-main,
  .modal-content-enhanced {
    border-width: 2px;
    border-color: var(--accent-color);
  }

  .tech-button-primary,
  .tech-button-secondary,
  .btn-primary {
    border: 2px solid currentColor;
  }
}

/* 暗色主题优化 */
@media (prefers-color-scheme: dark) {
  .form-input-enhanced {
    background: rgba(0, 0, 0, 0.5);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .sample-card {
    background: rgba(0, 0, 0, 0.3);
  }
}