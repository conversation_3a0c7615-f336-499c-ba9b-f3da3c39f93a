# 世界模型可视化展示大屏

一个现代化的路径编辑规划系统，提供直观的3D可视化界面和强大的路径编辑功能。

## ✨ 主要特性

### 🎨 现代化UI设计
- **响应式布局**: 基于Tailwind CSS，完美适配各种设备
- **玻璃态效果**: 现代化的毛玻璃背景和渐变设计
- **动画交互**: 流畅的页面动画和交互反馈
- **深色主题**: 专业的深色界面，减少眼部疲劳

### 🛠️ 功能模块

#### 导航系统
- 顶部导航栏，包含Logo、菜单和工具按钮
- 移动端适配的汉堡菜单
- 全屏模式支持
- 实时状态指示器

#### 样本数据管理
- 多样本数据选择界面
- 样本预览和统计信息
- 数据加载状态提示
- 样本类型分类（城市、山区、高速等）

#### 3D路径编辑工作台
- **工具栏**: 选择、移动、旋转、添加、删除等工具
- **视图控制**: 2D/3D视图切换
- **缩放控制**: 放大、缩小、适应窗口
- **实时预览**: 路径点的实时3D渲染

#### 视频播放器
- 原视频显示面板
- 视频控制按钮（播放/暂停、重启、音量）
- 进度条和时间显示
- 全屏播放支持
- 视频分析信息

#### 路径点编辑
- 坐标精确设置（X、Y轴）
- 角度调整（0-360°）
- 可视化角度预览
- 高级设置选项
- 批量编辑功能

### 🎮 交互操作

#### 鼠标操作
- **左键**: 选择和移动路径点
- **右键**: 旋转路径点方向
- **双击**: 打开路径点编辑面板
- **滚轮**: 缩放视图
- **中键拖拽**: 平移视图

#### 键盘快捷键
- **M**: 切换移动/旋转模式
- **R**: 重置视图
- **方向键**: 微调角度
- **Ctrl+S**: 保存项目
- **Ctrl+Z**: 撤销操作
- **Ctrl+Y**: 重做操作

### 📊 数据统计

#### 实时监控
- 路径点数量统计
- 路径总长度计算
- 平均角度分析
- 渲染性能监控（FPS、内存使用）

#### 操作历史
- 最近操作记录
- 操作时间戳
- 操作类型分类

## 🚀 技术栈

- **前端框架**: 原生JavaScript + HTML5 + CSS3
- **样式框架**: Tailwind CSS
- **3D渲染**: Three.js
- **图标库**: Font Awesome 6
- **构建工具**: 无需构建，直接运行

## 📦 安装和运行

### 环境要求
- 现代浏览器（Chrome 80+, Firefox 75+, Safari 13+）
- Python 3.x（用于本地服务器）

### 快速开始

1. **克隆项目**
```bash
git clone <repository-url>
cd world_models
```

2. **启动本地服务器**
```bash
python -m http.server 8000
```

3. **打开浏览器**
访问 `http://localhost:8000`

## 🎯 使用指南

### 基本工作流程

1. **选择样本数据**
   - 点击"样本选择"按钮
   - 从弹出的模态框中选择合适的样本
   - 确认选择后系统将加载数据

2. **编辑路径点**
   - 使用工具栏选择编辑工具
   - 在3D视图中点击路径点进行选择
   - 拖拽移动或右键旋转
   - 双击打开详细编辑面板

3. **调整视图**
   - 使用缩放工具调整视图大小
   - 切换2D/3D视图模式
   - 使用全屏模式获得更大工作空间

4. **保存和导出**
   - 使用快捷操作栏保存项目
   - 导出路径数据
   - 查看操作历史

### 高级功能

#### 批量编辑
- 选择多个路径点
- 统一调整角度或位置
- 批量删除或复制

#### 性能优化
- 实时FPS监控
- 内存使用情况
- 渲染质量调整

## 🎨 界面预览

### 主界面
- 现代化的深色主题
- 清晰的功能分区
- 直观的操作界面

### 样本选择
- 卡片式样本展示
- 详细的样本信息
- 状态指示器

### 路径编辑
- 3D可视化工作台
- 丰富的编辑工具
- 实时预览效果

## 🔧 自定义配置

### 主题定制
可以通过修改CSS变量来自定义主题颜色：

```css
:root {
  --primary-color: #0a0e1a;
  --accent-color: #06b6d4;
  --secondary-color: #3b82f6;
  /* 更多颜色变量... */
}
```

### 功能扩展
- 添加新的样本类型
- 扩展编辑工具
- 集成外部数据源

## 📱 响应式设计

- **桌面端**: 完整功能体验
- **平板端**: 优化的触控界面
- **手机端**: 简化的移动体验

## 🌐 浏览器兼容性

| 浏览器 | 版本要求 | 支持状态 |
|--------|----------|----------|
| Chrome | 80+ | ✅ 完全支持 |
| Firefox | 75+ | ✅ 完全支持 |
| Safari | 13+ | ✅ 完全支持 |
| Edge | 80+ | ✅ 完全支持 |

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

MIT License

---

**享受现代化的路径编辑体验！** 🚀